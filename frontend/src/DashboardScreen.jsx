import React, { useEffect, useState, useCallback, useMemo } from 'react';
import axiosInstance from './axiosInstance';
import { format } from 'date-fns';
import { useRealTimeData } from './hooks/useRealTimeData.jsx';
// import { useScreenData } from './contexts/PreloaderContext';
// import { ScreenPreloadIndicator } from './components/PreloaderStatus';
import ErrorBoundary from './components/ErrorBoundary.jsx';

const DashboardScreen = () => {
  // Bot control states
  const [alphaBotStatus, setAlphaBotStatus] = useState('stopped');
  const [microBotStatus, setMicroBotStatus] = useState('stopped');
  const [microBotStats, setMicroBotStats] = useState(null);

  // Modal states
  const [selectedSignal, setSelectedSignal] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // Real-time data hooks with optimized intervals
  const {
    data: summary,
    loading: loadingSummary,
    error: errorSummary
  } = useRealTimeData('/api/summary', {
    refreshInterval: 45000, // 45 seconds for summary
    dataImportance: 'high',
    onError: (err) => console.error('❌ Summary fetch failed:', err)
  });

  const {
    data: liveTrades,
    loading: loadingTrades,
    error: errorTrades
  } = useRealTimeData('/api/trades/live', {
    refreshInterval: 30000, // 30 seconds for live trades
    dataImportance: 'critical',
    transform: (data) => data?.trades || data || [],
    onError: (err) => console.error('❌ Live trades fetch failed:', err)
  });

  const {
    data: aiSignals,
    loading: loadingSignals,
    error: errorSignals
  } = useRealTimeData('/api/dashboard/ai-signals-fast', {
    refreshInterval: 60000, // 1 minute for AI signals
    dataImportance: 'high',
    transform: (data) => data?.signals || data || [],
    onError: (err) => console.error('❌ AI signals fetch failed:', err)
  });

  // Optimized callback functions
  const onSignalClick = useCallback((signal) => {
    setSelectedSignal(signal);
    setShowModal(true);
  }, []);

  // Bot status management with error handling
  const fetchAlphaBotStatus = useCallback(async () => {
    try {
      const response = await axiosInstance.get('/api/alpha-bot/status');
      setAlphaBotStatus(response.data.status);
    } catch (error) {
      console.error('❌ Failed to fetch alpha bot status:', error);
      setAlphaBotStatus('error');
    }
  }, []);

  const startAlphaBot = useCallback(async () => {
    try {
      setAlphaBotStatus('starting');
      const response = await axiosInstance.post('/api/alpha-bot/start');

      if (response.data.success) {
        console.log('✅ AlphaBot start initiated:', response.data.message);
        // Wait a moment then check status
        setTimeout(() => fetchAlphaBotStatus(), 2000);
      } else {
        console.error('❌ AlphaBot start failed:', response.data.message);
        setAlphaBotStatus('error');
      }
    } catch (error) {
      console.error('❌ Failed to start alpha bot:', error);
      setAlphaBotStatus('error');
    }
  }, [fetchAlphaBotStatus]);

  const stopAlphaBot = useCallback(async () => {
    try {
      setAlphaBotStatus('stopping');
      await axiosInstance.post('/api/alpha-bot/stop');
      await fetchAlphaBotStatus();
    } catch (error) {
      console.error('❌ Failed to stop alpha bot:', error);
      setAlphaBotStatus('error');
    }
  }, [fetchAlphaBotStatus]);

  const fetchMicroBotStatus = useCallback(async () => {
    try {
      const response = await axiosInstance.get('/api/micro-bot/status');
      setMicroBotStatus(response.data.status);
      setMicroBotStats(response.data.stats);
    } catch (error) {
      console.error('❌ Failed to fetch micro bot status:', error);
      setMicroBotStatus('error');
    }
  }, []);

  const startMicroBot = useCallback(async () => {
    try {
      setMicroBotStatus('starting');
      const response = await axiosInstance.post('/api/micro-bot/start');

      if (response.data.success) {
        console.log('✅ Enhanced MicroBot start initiated:', response.data.message);
        // Wait a moment then check status
        setTimeout(() => fetchMicroBotStatus(), 2000);
      } else {
        console.error('❌ Enhanced MicroBot start failed:', response.data.message);
        setMicroBotStatus('error');
      }
    } catch (error) {
      console.error('❌ Failed to start micro bot:', error);
      setMicroBotStatus('error');
    }
  }, [fetchMicroBotStatus]);

  const stopMicroBot = useCallback(async () => {
    try {
      setMicroBotStatus('stopping');
      await axiosInstance.post('/api/micro-bot/stop');
      await fetchMicroBotStatus();
    } catch (error) {
      console.error('❌ Failed to stop micro bot:', error);
      setMicroBotStatus('error');
    }
  }, [fetchMicroBotStatus]);

  // Bot status polling with optimized intervals
  useEffect(() => {
    fetchAlphaBotStatus();
    fetchMicroBotStatus();

    const interval = setInterval(() => {
      fetchAlphaBotStatus();
      fetchMicroBotStatus();
    }, 10000); // Reduced to 10 seconds for better performance

    return () => clearInterval(interval);
  }, [fetchAlphaBotStatus, fetchMicroBotStatus]);

  // Memoized computed values for better performance
  const dashboardStats = useMemo(() => {
    if (!summary) return null;

    return {
      totalTrades: summary.total_trades || 0,
      totalPnL: summary.total_pnl || 0,
      winRate: summary.win_rate || 0,
      avgTradeSize: summary.avg_trade_size || 0,
      todayTrades: summary.today_trades || 0,
      todayPnL: summary.today_pnl || 0
    };
  }, [summary]);

  const recentTrades = useMemo(() => {
    if (!Array.isArray(liveTrades)) return [];
    return liveTrades.slice(0, 10); // Show only last 10 trades
  }, [liveTrades]);

  const activeSignals = useMemo(() => {
    if (!Array.isArray(aiSignals)) return [];
    return aiSignals.filter(signal => signal.status === 'active').slice(0, 5);
  }, [aiSignals]);

  // Show loading state only if all critical data is loading
  if (loadingSummary && loadingTrades && loadingSignals) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div className="text-white text-lg">Loading dashboard...</div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="p-6 bg-gray-900 text-white min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              🚀 Alpha Predator Dashboard
            </h1>
            {/* <ScreenPreloadIndicator screenKey="dashboard" /> */}
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => window.location.reload()}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm transition-colors"
              title="Refresh dashboard data"
            >
              🔄 Refresh
            </button>
            <div className="text-sm text-gray-400">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>

        {/* Enhanced Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gradient-to-br from-blue-600 to-blue-800 p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-blue-200">Total Trades</h3>
                <p className="text-3xl font-bold text-white">{dashboardStats?.totalTrades ?? 0}</p>
              </div>
              <div className="text-blue-300">📊</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-600 to-green-800 p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-green-200">Total P&L</h3>
                <p className={`text-3xl font-bold ${(dashboardStats?.totalPnL ?? 0) >= 0 ? 'text-white' : 'text-red-300'}`}>
                  ${dashboardStats?.totalPnL?.toFixed(2) ?? '0.00'}
                </p>
              </div>
              <div className="text-green-300">💰</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-600 to-purple-800 p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-purple-200">Win Rate</h3>
                <p className="text-3xl font-bold text-white">{(dashboardStats?.winRate ?? 0).toFixed(1)}%</p>
              </div>
              <div className="text-purple-300">🎯</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-600 to-orange-800 p-6 rounded-lg shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-orange-200">Today's Trades</h3>
                <p className="text-3xl font-bold text-white">{dashboardStats?.todayTrades ?? 0}</p>
              </div>
              <div className="text-orange-300">📈</div>
            </div>
          </div>
        </div>

        {/* Enhanced Bot Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-blue-400">🤖 Alpha Predator Bot</h2>
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${alphaBotStatus === 'running' ? 'bg-green-900 text-green-300' :
                alphaBotStatus === 'starting' || alphaBotStatus === 'stopping' ? 'bg-yellow-900 text-yellow-300' :
                  alphaBotStatus === 'error' ? 'bg-red-900 text-red-300' :
                    'bg-gray-700 text-gray-300'
                }`}>
                <div className={`w-2 h-2 rounded-full ${alphaBotStatus === 'running' ? 'bg-green-400 animate-pulse' :
                  alphaBotStatus === 'starting' || alphaBotStatus === 'stopping' ? 'bg-yellow-400 animate-pulse' :
                    alphaBotStatus === 'error' ? 'bg-red-400' :
                      'bg-gray-400'
                  }`}></div>
                <span className="capitalize">{alphaBotStatus}</span>
                {alphaBotStatus === 'starting' && <span className="text-xs text-yellow-400 ml-2">Initializing...</span>}
                {alphaBotStatus === 'running' && <span className="text-xs text-green-400 ml-2">Active</span>}
                {alphaBotStatus === 'error' && <span className="text-xs text-red-400 ml-2">Error</span>}
              </div>
            </div>
            <button
              onClick={alphaBotStatus === 'running' ? stopAlphaBot : startAlphaBot}
              disabled={alphaBotStatus === 'starting' || alphaBotStatus === 'stopping'}
              className={`w-full px-4 py-3 rounded-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${alphaBotStatus === 'running'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
            >
              {alphaBotStatus === 'starting' ? 'Starting...' :
                alphaBotStatus === 'stopping' ? 'Stopping...' :
                  alphaBotStatus === 'running' ? '⏹️ Stop Bot' : '▶️ Start Bot'}
            </button>
          </div>

          <div className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-purple-400">⚡ Micro Bot</h2>
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${microBotStatus === 'running' ? 'bg-green-900 text-green-300' :
                microBotStatus === 'starting' || microBotStatus === 'stopping' ? 'bg-yellow-900 text-yellow-300' :
                  microBotStatus === 'error' ? 'bg-red-900 text-red-300' :
                    'bg-gray-700 text-gray-300'
                }`}>
                <div className={`w-2 h-2 rounded-full ${microBotStatus === 'running' ? 'bg-green-400 animate-pulse' :
                  microBotStatus === 'starting' || microBotStatus === 'stopping' ? 'bg-yellow-400 animate-pulse' :
                    microBotStatus === 'error' ? 'bg-red-400' :
                      'bg-gray-400'
                  }`}></div>
                <span className="capitalize">{microBotStatus}</span>
              </div>
            </div>

            {microBotStats && (
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{microBotStats.total_trades || 0}</div>
                  <div className="text-sm text-gray-400">Total Trades</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">${(microBotStats.total_profit || 0).toFixed(2)}</div>
                  <div className="text-sm text-gray-400">Total Profit</div>
                </div>
              </div>
            )}

            <button
              onClick={microBotStatus === 'running' ? stopMicroBot : startMicroBot}
              disabled={microBotStatus === 'starting' || microBotStatus === 'stopping'}
              className={`w-full px-4 py-3 rounded-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${microBotStatus === 'running'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-purple-600 hover:bg-purple-700 text-white'
                }`}
            >
              {microBotStatus === 'starting' ? 'Starting...' :
                microBotStatus === 'stopping' ? 'Stopping...' :
                  microBotStatus === 'running' ? '⏹️ Stop Micro Bot' : '▶️ Start Micro Bot'}
            </button>
          </div>
        </div>

        {/* Enhanced Live Trades Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-green-400">📊 Recent Trades</h2>
              <div className="text-sm text-gray-400">
                {loadingTrades && <span className="text-yellow-400">Updating...</span>}
                {errorTrades && <span className="text-red-400">Error</span>}
              </div>
            </div>

            {errorTrades ? (
              <div className="text-center py-8 text-red-400">
                <div className="text-4xl mb-2">⚠️</div>
                <div>Failed to load trades</div>
              </div>
            ) : recentTrades.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                <div className="text-4xl mb-2">📈</div>
                <div>No recent trades</div>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {recentTrades.map((trade, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${trade.type === 'buy' ? 'bg-green-400' : 'bg-red-400'}`}></div>
                      <div>
                        <div className="font-medium">{trade.token}</div>
                        <div className="text-sm text-gray-400">
                          {trade.time && !isNaN(new Date(trade.time).getTime())
                            ? format(new Date(trade.time), 'HH:mm:ss')
                            : 'N/A'}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${trade.type === 'buy' ? 'text-green-400' : 'text-red-400'}`}>
                        {trade.type?.toUpperCase()}
                      </div>
                      <div className="text-sm text-gray-400">
                        {typeof trade.price === 'number' ? `$${trade.price.toFixed(4)}` : 'N/A'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            {/* Enhanced AI Signals Section */}
            <div className="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-purple-400">🤖 AI Trading Signals</h2>
                <div className="text-sm text-gray-400">
                  {loadingSignals && <span className="text-yellow-400">Updating...</span>}
                  {errorSignals && <span className="text-red-400">Error</span>}
                </div>
              </div>

              {errorSignals ? (
                <div className="text-center py-8 text-red-400">
                  <div className="text-4xl mb-2">🚫</div>
                  <div>Failed to load AI signals</div>
                </div>
              ) : activeSignals.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  <div className="text-4xl mb-2">🤖</div>
                  <div>No active AI signals</div>
                </div>
              ) : (
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {activeSignals.map((signal, index) => (
                    <div
                      key={index}
                      className="p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors cursor-pointer"
                      onClick={() => onSignalClick(signal)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-bold text-lg">{signal.symbol}</div>
                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${signal.final_decision === 'BUY' ? 'bg-green-900 text-green-300' :
                          signal.final_decision === 'SELL' ? 'bg-red-900 text-red-300' :
                            'bg-yellow-900 text-yellow-300'
                          }`}>
                          {signal.final_decision}
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-2 text-sm mb-2">
                        <div className="text-center">
                          <div className="text-blue-400">🧠 DeepSeek</div>
                          <div className="font-medium">{signal.deepseek}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-purple-400">✨ Gemini</div>
                          <div className="font-medium">{signal.gemini}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-green-400">🔮 OpenAI</div>
                          <div className="font-medium">{signal.openai}</div>
                        </div>
                      </div>

                      <div className="flex justify-between items-center text-sm text-gray-400">
                        <span>Confidence: {Math.round((signal.confidence || 0) * 100)}%</span>
                        <span>Click for details</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {showModal && selectedSignal && (
          <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
            <div className="bg-gray-900 p-6 rounded shadow-lg w-full max-w-lg text-white">
              <h2 className="text-xl font-bold mb-4">
                Trade Signal Breakdown: {selectedSignal.symbol}
              </h2>

              <div className="mb-6 space-y-1">
                <p>
                  <strong>🧠 DeepSeek:</strong> {selectedSignal.deepseek}
                </p>
                <p>
                  <strong>✨ Gemini:</strong> {selectedSignal.gemini}</p>
                <p>
                  <strong>🔮 OpenAI:</strong> {selectedSignal.openai}</p>
                <p>
                  <strong>✅ Final Decision:</strong>{' '}
                  <span
                    className={
                      selectedSignal.final_decision === 'BUY'
                        ? 'text-green-400'
                        : selectedSignal.final_decision === 'SELL'
                          ? 'text-red-400'
                          : 'text-yellow-300'
                    }
                  >
                    {selectedSignal.final_decision}
                  </span>
                </p>

                <p>
                  <strong>📊 Confidence:</strong> {Math.round(selectedSignal.confidence * 100)}%
                </p>
              </div>

              <button
                className="mt-2 px-4 py-2 bg-red-500 hover:bg-red-600 rounded"
                onClick={() => setShowModal(false)}
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
};

export default DashboardScreen;

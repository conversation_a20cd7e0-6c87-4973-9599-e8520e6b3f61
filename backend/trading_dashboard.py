#!/usr/bin/env python3
"""
Advanced Trading Dashboard
=========================
Real-time monitoring dashboard for the advanced trading system
"""

import sys
import time
import json
import os
from datetime import datetime
from typing import Dict, Any

# Now in backend directory - no path manipulation needed


def load_portfolio_data() -> Dict[str, Any]:
    """Load portfolio data from the advanced trading system"""
    try:
        portfolio_file = "data/advanced_portfolio.json"
        if os.path.exists(portfolio_file):
            with open(portfolio_file, "r") as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading portfolio: {e}")

    return {"positions": {}, "last_updated": "Never"}


def load_trade_logs() -> list:
    """Load recent trade logs"""
    try:
        from backend.trade_executor import load_portfolio

        # This is a simplified version - in production you'd load from CSV
        return []
    except:
        return []


def display_dashboard():
    """Display the trading dashboard"""
    os.system("clear" if os.name == "posix" else "cls")

    print("🤖 ADVANCED TRADING DASHBOARD")
    print("=" * 80)
    print(f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Load data
    portfolio_data = load_portfolio_data()
    positions = portfolio_data.get("positions", {})

    if not positions:
        print("📊 No active positions")
        print()
        print("🔍 System Status:")
        print("   • Scanning for opportunities...")
        print("   • AI models: Active")
        print("   • KuCoin connection: Connected")
        print()
        return

    # Calculate totals
    total_value = 0
    total_pnl = 0
    total_entry_value = 0

    print("📊 ACTIVE POSITIONS")
    print("-" * 80)
    print(
        f"{'Symbol':<12} {'Entry $':<10} {'Current $':<10} {'Quantity':<12} {'P&L $':<10} {'P&L %':<8} {'Time':<10}"
    )
    print("-" * 80)

    try:
        from backend.kucoin_api import KuCoinAPI

        kucoin = KuCoinAPI()

        for symbol, pos_data in positions.items():
            try:
                # Get current price
                ticker = kucoin.get_ticker(symbol)
                current_price = float(ticker.get("price", 0)) if ticker else 0

                entry_price = pos_data["entry_price"]
                quantity = pos_data["quantity"]
                entry_value = entry_price * quantity
                current_value = current_price * quantity
                pnl = current_value - entry_value
                pnl_percentage = (pnl / entry_value) * 100 if entry_value > 0 else 0

                total_value += current_value
                total_pnl += pnl
                total_entry_value += entry_value

                # Time held
                entry_time = datetime.fromisoformat(pos_data["entry_time"])
                time_held = datetime.now() - entry_time
                time_str = (
                    f"{time_held.days}d"
                    if time_held.days > 0
                    else f"{time_held.seconds//3600}h"
                )

                # Color coding for P&L
                pnl_color = "🟢" if pnl > 0 else "🔴" if pnl < 0 else "⚪"

                print(
                    f"{symbol:<12} ${entry_price:<9.4f} ${current_price:<9.4f} {quantity:<12.6f} {pnl_color}${pnl:<9.2f} {pnl_percentage:<7.1f}% {time_str:<10}"
                )

                # Show profit levels
                profit_levels = pos_data.get("profit_levels", [])
                executed_levels = sum(
                    1 for level in profit_levels if level.get("executed", False)
                )
                if executed_levels > 0:
                    print(
                        f"             └─ Profit levels executed: {executed_levels}/{len(profit_levels)}"
                    )

            except Exception as e:
                print(f"{symbol:<12} Error loading data: {e}")

    except Exception as e:
        print(f"Error connecting to KuCoin: {e}")

    print("-" * 80)

    # Summary
    total_pnl_percentage = (
        (total_pnl / total_entry_value) * 100 if total_entry_value > 0 else 0
    )
    pnl_color = "🟢" if total_pnl > 0 else "🔴" if total_pnl < 0 else "⚪"

    print(f"TOTAL POSITIONS: {len(positions)}")
    print(f"TOTAL VALUE: ${total_value:.2f}")
    print(f"TOTAL P&L: {pnl_color}${total_pnl:.2f} ({total_pnl_percentage:.2f}%)")
    print()

    # System status
    print("🔍 SYSTEM STATUS")
    print("-" * 40)

    try:
        from backend.config import TRADING_MODE

        print(f"Trading Mode: {TRADING_MODE}")

        # Check if orchestrator is running
        if os.path.exists("advanced_trading.log"):
            with open("advanced_trading.log", "r") as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1]
                    if "Enhanced trade cycle" in last_line or "Managing" in last_line:
                        print("Bot Status: 🟢 Active")
                    else:
                        print("Bot Status: 🟡 Unknown")
                else:
                    print("Bot Status: 🔴 Not running")
        else:
            print("Bot Status: 🔴 Not running")

    except Exception as e:
        print(f"Status check error: {e}")

    print()
    print("💡 STRATEGY INFO")
    print("-" * 40)
    print("• Multi-level profit taking: 3%, 6%, 10%, 15%")
    print("• Trailing stop loss: 10%")
    print("• AI confidence threshold: 60%")
    print("• Max positions: 20")
    print("• Cycle interval: 4 minutes")
    print("• API optimization: Reduced calls")
    print()
    print("Press Ctrl+C to exit dashboard")


def main():
    """Main dashboard loop"""
    print("Starting Advanced Trading Dashboard...")
    print("Press Ctrl+C to exit")

    try:
        while True:
            display_dashboard()
            time.sleep(30)  # Update every 30 seconds
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped")
    except Exception as e:
        print(f"\n❌ Dashboard error: {e}")


if __name__ == "__main__":
    main()

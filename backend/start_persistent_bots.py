#!/usr/bin/env python3
"""
Persistent Bot Runner
====================

This script ensures the Alpha Predator and Micro bots run continuously 24/7.
It includes automatic restart capabilities and comprehensive error handling.
"""

import asyncio
import logging
import signal
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append('backend')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backend/logs/persistent_bots.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PersistentBotRunner:
    def __init__(self):
        self.running = True
        self.alpha_bot_task = None
        self.micro_bot_task = None
        self.restart_count = 0
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
    
    async def run_alpha_bot(self):
        """Run Alpha Predator bot with error recovery"""
        while self.running:
            try:
                from live_runner import run_live_cycle
                await run_live_cycle()
                await asyncio.sleep(60)  # Wait 1 minute between cycles
            except Exception as e:
                logger.error(f"Alpha bot error: {e}")
                await asyncio.sleep(30)  # Wait 30 seconds before retry
    
    async def run_micro_bot(self):
        """Run Micro bot with error recovery"""
        while self.running:
            try:
                from micro_bot import micro_bot_main  # Update to actual function/class name
                await micro_bot_main()  # Call the main function/class for micro bot
                await asyncio.sleep(30)  # Wait 30 seconds between cycles
            except Exception as e:
                logger.error(f"Micro bot error: {e}")
                await asyncio.sleep(15)  # Wait 15 seconds before retry
    
    async def start(self):
        """Start both bots concurrently"""
        logger.info("Starting persistent bot runner...")
        
        # Start both bots as concurrent tasks
        self.alpha_bot_task = asyncio.create_task(self.run_alpha_bot())
        self.micro_bot_task = asyncio.create_task(self.run_micro_bot())
        
        # Wait for both tasks
        await asyncio.gather(
            self.alpha_bot_task,
            self.micro_bot_task,
            return_exceptions=True
        )

if __name__ == "__main__":
    runner = PersistentBotRunner()
    asyncio.run(runner.start())

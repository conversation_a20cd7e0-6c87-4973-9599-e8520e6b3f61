#!/usr/bin/env python3
"""
🔗 DISCORD NEWS BOT - KRYPTONEWSDAILY INTEGRATION
Monitors Discord channel for crypto news and sentiment
"""

import asyncio
import json
import logging
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Discord configuration
DISCORD_CHANNEL_ID = "kryptonewsdaily"  # Channel to monitor
NEWS_CACHE_FILE = "data/discord_news_cache.json"
CACHE_DURATION_HOURS = 24

# News patterns for filtering
CRYPTO_KEYWORDS = [
    "bitcoin", "btc", "ethereum", "eth", "crypto", "cryptocurrency", 
    "blockchain", "defi", "nft", "altcoin", "trading", "pump", "dump",
    "bull", "bear", "market", "price", "volume", "listing", "exchange"
]

TOKEN_PATTERNS = [
    r'\$[A-Z]{2,10}',  # $BTC, $ETH format
    r'#[A-Z]{2,10}',   # #BTC, #ETH format
    r'\b[A-Z]{2,10}-USDT\b',  # BTC-USDT format
    r'\b[A-Z]{2,10}/USDT\b',  # BTC/USDT format
]

class DiscordNewsMonitor:
    """Monitor Discord for crypto news"""
    
    def __init__(self):
        self.news_cache = self._load_cache()
        self.session = None
    
    def _load_cache(self) -> List[Dict]:
        """Load cached news from file"""
        try:
            with open(NEWS_CACHE_FILE, 'r') as f:
                cache_data = json.load(f)
                # Filter out old news (older than CACHE_DURATION_HOURS)
                cutoff_time = datetime.now() - timedelta(hours=CACHE_DURATION_HOURS)
                fresh_news = [
                    news for news in cache_data 
                    if datetime.fromisoformat(news.get('timestamp', '2020-01-01')) > cutoff_time
                ]
                return fresh_news
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_cache(self):
        """Save news cache to file"""
        try:
            import os
            os.makedirs(os.path.dirname(NEWS_CACHE_FILE), exist_ok=True)
            with open(NEWS_CACHE_FILE, 'w') as f:
                json.dump(self.news_cache, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save Discord news cache: {e}")
    
    def extract_tokens_from_text(self, text: str) -> List[str]:
        """Extract token symbols from text"""
        tokens = []
        text_upper = text.upper()
        
        # Extract using patterns
        for pattern in TOKEN_PATTERNS:
            matches = re.findall(pattern, text_upper)
            tokens.extend(matches)
        
        # Clean up tokens
        cleaned_tokens = []
        for token in tokens:
            # Remove special characters
            clean_token = re.sub(r'[^A-Z]', '', token)
            if 2 <= len(clean_token) <= 10:  # Valid token length
                cleaned_tokens.append(clean_token)
        
        return list(set(cleaned_tokens))  # Remove duplicates
    
    def calculate_sentiment_score(self, text: str) -> float:
        """Calculate sentiment score from text"""
        positive_words = [
            "bullish", "pump", "moon", "rocket", "gain", "profit", "up", "rise",
            "surge", "rally", "breakout", "support", "buy", "long", "hodl"
        ]
        
        negative_words = [
            "bearish", "dump", "crash", "fall", "drop", "loss", "down", "decline",
            "sell", "short", "fear", "panic", "resistance", "breakdown"
        ]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_words = len(text.split())
        if total_words == 0:
            return 0.5
        
        # Calculate sentiment score (0.0 = very negative, 1.0 = very positive)
        sentiment = 0.5 + (positive_count - negative_count) / (total_words * 2)
        return max(0.0, min(1.0, sentiment))
    
    def is_crypto_related(self, text: str) -> bool:
        """Check if text is crypto-related"""
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in CRYPTO_KEYWORDS)
    
    def process_discord_message(self, message_data: Dict) -> Optional[Dict]:
        """Process a Discord message and extract news data"""
        try:
            content = message_data.get('content', '')
            author = message_data.get('author', {}).get('username', 'Unknown')
            timestamp = message_data.get('timestamp', datetime.now().isoformat())
            
            # Skip if not crypto-related
            if not self.is_crypto_related(content):
                return None
            
            # Extract tokens mentioned
            tokens = self.extract_tokens_from_text(content)
            
            # Calculate sentiment
            sentiment_score = self.calculate_sentiment_score(content)
            
            # Create news item
            news_item = {
                'id': f"discord_{hash(content)}_{timestamp}",
                'title': content[:100] + "..." if len(content) > 100 else content,
                'content': content,
                'author': author,
                'source': 'discord',
                'channel': DISCORD_CHANNEL_ID,
                'tokens_mentioned': tokens,
                'sentiment_score': sentiment_score,
                'sentiment': 'positive' if sentiment_score > 0.6 else 'negative' if sentiment_score < 0.4 else 'neutral',
                'timestamp': timestamp,
                'created_at': timestamp,
                'credibility': 0.6,  # Medium credibility for Discord
                'type': 'social',
                'platform': 'discord'
            }
            
            return news_item
            
        except Exception as e:
            logger.error(f"Error processing Discord message: {e}")
            return None
    
    def add_news_item(self, news_item: Dict):
        """Add news item to cache"""
        if news_item:
            # Check for duplicates
            existing_ids = [item.get('id') for item in self.news_cache]
            if news_item.get('id') not in existing_ids:
                self.news_cache.append(news_item)
                # Keep only recent news
                cutoff_time = datetime.now() - timedelta(hours=CACHE_DURATION_HOURS)
                self.news_cache = [
                    item for item in self.news_cache
                    if datetime.fromisoformat(item.get('timestamp', '2020-01-01')) > cutoff_time
                ]
                self._save_cache()
                logger.info(f"Added Discord news item: {news_item.get('title', '')[:50]}...")
    
    def get_latest_news(self, limit: int = 20, token_filter: Optional[str] = None) -> List[Dict]:
        """Get latest news from cache"""
        news = self.news_cache.copy()
        
        # Filter by token if specified
        if token_filter:
            token_upper = token_filter.upper()
            news = [
                item for item in news
                if token_upper in item.get('tokens_mentioned', []) or
                   token_upper in item.get('content', '').upper()
            ]
        
        # Sort by timestamp (newest first)
        news.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
        
        return news[:limit]

# Global monitor instance
discord_monitor = DiscordNewsMonitor()

def get_latest_discord_news(limit: int = 20, token: Optional[str] = None) -> List[Dict]:
    """Get latest Discord news (public function for API)"""
    return discord_monitor.get_latest_news(limit=limit, token_filter=token)

def add_discord_news(message_content: str, author: str = "kryptonewsdaily") -> bool:
    """Add Discord news manually (for testing or webhook integration)"""
    try:
        message_data = {
            'content': message_content,
            'author': {'username': author},
            'timestamp': datetime.now().isoformat()
        }
        
        news_item = discord_monitor.process_discord_message(message_data)
        if news_item:
            discord_monitor.add_news_item(news_item)
            return True
        return False
        
    except Exception as e:
        logger.error(f"Error adding Discord news: {e}")
        return False

def simulate_discord_news():
    """Simulate Discord news for testing"""
    sample_messages = [
        "🚀 $BTC breaking resistance at $45,000! Bull run incoming? #Bitcoin #Crypto",
        "⚠️ $ETH showing bearish signals, possible dump to $2,800 support level",
        "🔥 New DeFi token $ALPHA launching on KuCoin tomorrow! Early bird gets the worm 🪱",
        "📈 $ADA pumping 15% after Cardano smart contract update announcement",
        "💎 HODL strong! $SOL forming bullish pattern, target $180 🎯",
        "🐻 Market looking shaky, $DOGE might test $0.08 support soon",
        "🌙 $MATIC to the moon! Polygon partnership with major bank confirmed",
        "⚡ Lightning Network adoption growing, $BTC long-term bullish outlook"
    ]
    
    for message in sample_messages:
        add_discord_news(message, "kryptonewsdaily_bot")
    
    logger.info(f"Simulated {len(sample_messages)} Discord news items")

if __name__ == "__main__":
    # Test the Discord news system
    print("🔗 Testing Discord News Integration")
    print("=" * 50)
    
    # Simulate some news
    simulate_discord_news()
    
    # Get latest news
    latest_news = get_latest_discord_news(limit=10)
    print(f"\n📰 Latest {len(latest_news)} Discord News Items:")
    
    for i, news in enumerate(latest_news, 1):
        print(f"{i}. {news.get('title', '')[:60]}...")
        print(f"   Tokens: {', '.join(news.get('tokens_mentioned', []))}")
        print(f"   Sentiment: {news.get('sentiment', 'neutral')} ({news.get('sentiment_score', 0):.2f})")
        print()
    
    # Test token-specific filtering
    btc_news = get_latest_discord_news(limit=5, token="BTC")
    print(f"\n₿ BTC-specific news ({len(btc_news)} items):")
    for news in btc_news:
        print(f"- {news.get('title', '')[:80]}...")

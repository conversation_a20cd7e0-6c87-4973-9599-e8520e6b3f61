"""
Advanced Trading Orchestrator
============================
Comprehensive automated trading system that:
1. Continuously buys tokens based on AI decisions
2. Monitors portfolio with advanced profit-taking strategies
3. Sets up multi-level exit orders at strategic price points
4. Manages risk and position sizing dynamically
"""

import asyncio
import logging
import time
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json

from config import BASE_ORDER_SIZE_USDT, TRADING_MODE, STOP_LOSS, TAKE_PROFIT, MAX_BUY
from real_trade_executor import real_trade_executor
from ai_clients.ai_request_manager import get_ai_decision_with_consensus
from token_selector import get_top_tokens_for_trading
from trade_executor import load_portfolio, save_portfolio
from kucoin_api import KuCoinAPI

logger = logging.getLogger(__name__)


@dataclass
class ProfitLevel:
    """Represents a profit-taking level"""

    percentage: float  # Profit percentage (e.g., 0.05 for 5%)
    quantity_ratio: float  # Portion of position to sell (e.g., 0.25 for 25%)
    order_type: str  # "limit" or "market"
    executed: bool = False


@dataclass
class PositionManager:
    """Manages individual token positions with advanced exit strategies"""

    symbol: str
    entry_price: float
    quantity: float
    entry_time: datetime
    profit_levels: List[ProfitLevel]
    stop_loss_price: float
    trailing_stop_enabled: bool = True
    highest_price: float = 0.0


class AdvancedTradingOrchestrator:
    """Advanced trading orchestrator with multi-level profit taking"""

    def __init__(self):
        self.is_running = False
        self.kucoin = KuCoinAPI()
        self.positions: Dict[str, PositionManager] = {}
        self.portfolio_file = "backend/data/advanced_portfolio.json"
        self.max_positions = 20  # Increased from 5 to 20 positions
        self.min_confidence = 0.60  # Reduced from 70% to 60% for more opportunities

        # API call optimization
        self.last_token_fetch = 0
        self.token_cache_duration = 300  # Cache tokens for 5 minutes
        self.cached_tokens = []

        # Advanced profit-taking levels
        self.default_profit_levels = [
            ProfitLevel(0.03, 0.25, "limit"),  # Take 25% profit at 3%
            ProfitLevel(0.06, 0.30, "limit"),  # Take 30% profit at 6%
            ProfitLevel(0.10, 0.25, "limit"),  # Take 25% profit at 10%
            ProfitLevel(0.15, 0.20, "market"),  # Take remaining 20% at 15%
        ]

        self.load_positions()

    def load_positions(self):
        """Load existing positions from file"""
        try:
            if os.path.exists(self.portfolio_file):
                with open(self.portfolio_file, "r") as f:
                    data = json.load(f)
                    for symbol, pos_data in data.get("positions", {}).items():
                        profit_levels = [
                            ProfitLevel(**level) for level in pos_data["profit_levels"]
                        ]
                        self.positions[symbol] = PositionManager(
                            symbol=pos_data["symbol"],
                            entry_price=pos_data["entry_price"],
                            quantity=pos_data["quantity"],
                            entry_time=datetime.fromisoformat(pos_data["entry_time"]),
                            profit_levels=profit_levels,
                            stop_loss_price=pos_data["stop_loss_price"],
                            trailing_stop_enabled=pos_data.get(
                                "trailing_stop_enabled", True
                            ),
                            highest_price=pos_data.get(
                                "highest_price", pos_data["entry_price"]
                            ),
                        )
                logger.info(f"Loaded {len(self.positions)} existing positions")
        except Exception as e:
            logger.error(f"Error loading positions: {e}")

    def save_positions(self):
        """Save positions to file"""
        try:
            data = {"positions": {}, "last_updated": datetime.now().isoformat()}

            for symbol, pos in self.positions.items():
                data["positions"][symbol] = {
                    "symbol": pos.symbol,
                    "entry_price": pos.entry_price,
                    "quantity": pos.quantity,
                    "entry_time": pos.entry_time.isoformat(),
                    "profit_levels": [
                        {
                            "percentage": level.percentage,
                            "quantity_ratio": level.quantity_ratio,
                            "order_type": level.order_type,
                            "executed": level.executed,
                        }
                        for level in pos.profit_levels
                    ],
                    "stop_loss_price": pos.stop_loss_price,
                    "trailing_stop_enabled": pos.trailing_stop_enabled,
                    "highest_price": pos.highest_price,
                }

            os.makedirs(os.path.dirname(self.portfolio_file), exist_ok=True)
            with open(self.portfolio_file, "w") as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving positions: {e}")

    async def start_orchestrator(self):
        """Start the advanced trading orchestrator"""
        logger.info("🚀 Starting Advanced Trading Orchestrator")
        self.is_running = True

        while self.is_running:
            try:
                cycle_start = time.time()

                # 1. Monitor and manage existing positions
                await self.manage_existing_positions()

                # 2. Look for new buying opportunities
                await self.find_new_opportunities()

                # 3. Update trailing stops and profit levels
                await self.update_dynamic_exits()

                # 4. Save state
                self.save_positions()

                cycle_time = time.time() - cycle_start
                logger.info(f"✅ Trading cycle completed in {cycle_time:.2f}s")

                # Wait before next cycle (4 minutes for reduced API calls)
                await asyncio.sleep(240)  # 4 minutes = 240 seconds

            except Exception as e:
                logger.error(f"Error in trading cycle: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def manage_existing_positions(self):
        """Monitor and manage existing positions with advanced exit strategies"""
        if not self.positions:
            return

        logger.info(f"📊 Managing {len(self.positions)} existing positions")

        for symbol, position in list(self.positions.items()):
            try:
                # Get current price
                ticker = self.kucoin.get_ticker(symbol)
                if not ticker:
                    continue

                current_price = float(ticker.get("price", 0))
                if current_price <= 0:
                    continue

                # Update highest price for trailing stop
                if current_price > position.highest_price:
                    position.highest_price = current_price

                # Check stop loss
                if await self.check_stop_loss(position, current_price):
                    continue  # Position was closed

                # Check profit levels
                await self.check_profit_levels(position, current_price)

            except Exception as e:
                logger.error(f"Error managing position {symbol}: {e}")

    async def check_stop_loss(
        self, position: PositionManager, current_price: float
    ) -> bool:
        """Check and execute stop loss if needed"""
        # Calculate trailing stop price
        if position.trailing_stop_enabled:
            trailing_stop_price = position.highest_price * (1 - STOP_LOSS)
            stop_price = max(position.stop_loss_price, trailing_stop_price)
        else:
            stop_price = position.stop_loss_price

        if current_price <= stop_price:
            logger.warning(
                f"🛑 Stop loss triggered for {position.symbol} at ${current_price:.4f}"
            )

            # Execute stop loss
            result = real_trade_executor.execute_real_trade(
                token_symbol=position.symbol,
                side="SELL",
                amount_usd=current_price * position.quantity,
                strategy="StopLoss",
                reason=f"Stop loss at ${current_price:.4f}",
            )

            if result.get("success"):
                logger.info(f"✅ Stop loss executed for {position.symbol}")
                del self.positions[position.symbol]
                return True
            else:
                logger.error(
                    f"❌ Stop loss failed for {position.symbol}: {result.get('message')}"
                )

        return False

    async def check_profit_levels(
        self, position: PositionManager, current_price: float
    ):
        """Check and execute profit-taking levels"""
        profit_percentage = (
            current_price - position.entry_price
        ) / position.entry_price

        for level in position.profit_levels:
            if not level.executed and profit_percentage >= level.percentage:
                # Calculate quantity to sell
                sell_quantity = position.quantity * level.quantity_ratio
                sell_amount = current_price * sell_quantity

                logger.info(
                    f"💰 Taking {level.percentage*100:.1f}% profit on {position.symbol}"
                )

                # Execute profit-taking order
                result = real_trade_executor.execute_real_trade(
                    token_symbol=position.symbol,
                    side="SELL",
                    amount_usd=sell_amount,
                    strategy="ProfitTaking",
                    reason=f"Take profit {level.percentage*100:.1f}% at ${current_price:.4f}",
                )

                if result.get("success"):
                    level.executed = True
                    position.quantity -= sell_quantity
                    logger.info(
                        f"✅ Profit taken: {level.percentage*100:.1f}% for {position.symbol}"
                    )

                    # Remove position if fully sold
                    if position.quantity <= 0.001:  # Small threshold for rounding
                        del self.positions[position.symbol]
                        break
                else:
                    logger.error(
                        f"❌ Profit taking failed for {position.symbol}: {result.get('message')}"
                    )

    async def find_new_opportunities(self):
        """Find and execute new buying opportunities based on AI decisions"""
        if len(self.positions) >= self.max_positions:
            logger.info(
                f"📊 Portfolio full ({len(self.positions)}/{self.max_positions} positions)"
            )
            return

        # Get top tokens for analysis with caching to reduce API calls
        current_time = time.time()
        if (
            current_time - self.last_token_fetch
        ) > self.token_cache_duration or not self.cached_tokens:
            logger.info("🔄 Refreshing token cache...")
            self.cached_tokens = get_top_tokens_for_trading(limit=15)
            self.last_token_fetch = current_time

        top_tokens = self.cached_tokens
        if not top_tokens:
            logger.warning("⚠️ No tokens found for analysis")
            return

        # Filter out tokens we already hold
        new_tokens = [
            token for token in top_tokens if token.get("symbol") not in self.positions
        ][
            :8
        ]  # Analyze top 8 new opportunities (reduced from 10)

        logger.info(f"🔍 Analyzing {len(new_tokens)} new opportunities")

        for token_data in new_tokens:
            symbol = None
            try:
                symbol = token_data.get("symbol")
                if not symbol:
                    continue

                # Get AI decision
                prompt = self.build_trading_prompt(token_data)
                ai_decision = get_ai_decision_with_consensus(prompt, symbol)

                decision = ai_decision.get("decision", "HOLD").upper()
                confidence = ai_decision.get("confidence", 0.0) / 100.0

                if decision == "BUY" and confidence >= self.min_confidence:
                    await self.execute_buy_order(
                        symbol, confidence, ai_decision.get("reason", "")
                    )

                    # Limit to one buy per cycle to avoid overexposure
                    break

            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")

    def build_trading_prompt(self, token_data: Dict[str, Any]) -> str:
        """Build comprehensive trading prompt for AI analysis"""
        symbol = token_data.get("symbol", "UNKNOWN")
        price = token_data.get("price", 0)
        volume = token_data.get("volume_24h", 0)
        change_24h = token_data.get("price_change_24h", 0)

        prompt = f"""
        ADVANCED TRADING ANALYSIS FOR {symbol}

        Current Market Data:
        - Price: ${price:.6f}
        - 24h Volume: ${volume:,.2f}
        - 24h Change: {change_24h:.2f}%

        Trading Context:
        - Strategy: Multi-level profit taking with trailing stops
        - Risk Management: 10% stop loss, 3-15% profit levels
        - Position Sizing: Dynamic based on confidence

        Provide BUY/SELL/HOLD decision with confidence (0-100) and detailed reasoning.
        Focus on short to medium-term profit potential (1-7 days).
        Consider technical indicators, market sentiment, and risk-reward ratio.
        """

        return prompt

    async def execute_buy_order(self, symbol: str, confidence: float, reason: str):
        """Execute buy order and set up position management"""
        try:
            # Calculate position size based on confidence
            base_size = BASE_ORDER_SIZE_USDT
            confidence_multiplier = 0.5 + (confidence * 1.5)  # 0.5x to 2.0x
            position_size = min(base_size * confidence_multiplier, base_size * 2)

            logger.info(
                f"🛒 Executing BUY order for {symbol} (${position_size:.2f}, confidence: {confidence:.2f})"
            )

            # Execute the trade
            result = real_trade_executor.execute_real_trade(
                token_symbol=symbol,
                side="BUY",
                amount_usd=position_size,
                strategy="AI_Advanced",
                reason=f"AI Buy (confidence: {confidence:.2f}) - {reason[:100]}",
            )

            if result.get("success"):
                # Get entry details
                entry_price = result.get("price", 0)
                quantity = result.get("amount", 0)

                # Create position manager
                position = PositionManager(
                    symbol=symbol,
                    entry_price=entry_price,
                    quantity=quantity,
                    entry_time=datetime.now(),
                    profit_levels=[
                        ProfitLevel(
                            level.percentage, level.quantity_ratio, level.order_type
                        )
                        for level in self.default_profit_levels
                    ],
                    stop_loss_price=entry_price * (1 - STOP_LOSS),
                    trailing_stop_enabled=True,
                    highest_price=entry_price,
                )

                self.positions[symbol] = position
                logger.info(f"✅ Position created for {symbol} at ${entry_price:.6f}")

            else:
                logger.error(
                    f"❌ Buy order failed for {symbol}: {result.get('message')}"
                )

        except Exception as e:
            logger.error(f"Error executing buy order for {symbol}: {e}")

    async def update_dynamic_exits(self):
        """Update dynamic exit strategies based on market conditions"""
        for symbol, position in self.positions.items():
            try:
                # Get current market data
                ticker = self.kucoin.get_ticker(symbol)
                if not ticker:
                    continue

                current_price = float(ticker.get("price", 0))
                if current_price <= 0:
                    continue

                # Update trailing stop based on volatility
                volatility = self.calculate_volatility(symbol)
                if volatility > 0.15:  # High volatility
                    # Tighter trailing stop for volatile tokens
                    position.trailing_stop_enabled = True
                elif volatility < 0.05:  # Low volatility
                    # Wider trailing stop for stable tokens
                    position.trailing_stop_enabled = True

                # Adjust profit levels based on time held
                time_held = datetime.now() - position.entry_time
                if time_held > timedelta(days=3):
                    # Reduce profit targets for positions held longer
                    for level in position.profit_levels:
                        if not level.executed:
                            level.percentage *= 0.9  # Reduce by 10%

            except Exception as e:
                logger.error(f"Error updating exits for {symbol}: {e}")

    def calculate_volatility(self, symbol: str) -> float:
        """Calculate simple volatility estimate"""
        try:
            # This is a simplified volatility calculation
            # In production, you'd want to use proper statistical methods
            ticker = self.kucoin.get_ticker(symbol)
            if ticker:
                # Use 24h change as a proxy for volatility
                change_24h = abs(float(ticker.get("changeRate", 0)))
                return change_24h
        except:
            pass
        return 0.1  # Default moderate volatility

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get comprehensive portfolio summary"""
        total_value = 0
        total_pnl = 0
        positions_summary = []

        for symbol, position in self.positions.items():
            try:
                ticker = self.kucoin.get_ticker(symbol)
                if ticker:
                    current_price = float(ticker.get("price", 0))
                    current_value = current_price * position.quantity
                    entry_value = position.entry_price * position.quantity
                    pnl = current_value - entry_value
                    pnl_percentage = (pnl / entry_value) * 100

                    total_value += current_value
                    total_pnl += pnl

                    positions_summary.append(
                        {
                            "symbol": symbol,
                            "entry_price": position.entry_price,
                            "current_price": current_price,
                            "quantity": position.quantity,
                            "entry_value": entry_value,
                            "current_value": current_value,
                            "pnl": pnl,
                            "pnl_percentage": pnl_percentage,
                            "time_held": str(datetime.now() - position.entry_time),
                            "profit_levels_executed": sum(
                                1 for level in position.profit_levels if level.executed
                            ),
                        }
                    )
            except Exception as e:
                logger.error(f"Error calculating summary for {symbol}: {e}")

        return {
            "total_positions": len(self.positions),
            "total_value": total_value,
            "total_pnl": total_pnl,
            "total_pnl_percentage": (
                (total_pnl / (total_value - total_pnl)) * 100
                if total_value > total_pnl
                else 0
            ),
            "positions": positions_summary,
            "last_updated": datetime.now().isoformat(),
        }

    async def stop_orchestrator(self):
        """Stop the orchestrator gracefully"""
        logger.info("🛑 Stopping Advanced Trading Orchestrator")
        self.is_running = False
        self.save_positions()


# Global instance
advanced_orchestrator = AdvancedTradingOrchestrator()

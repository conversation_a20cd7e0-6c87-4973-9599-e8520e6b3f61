#!/usr/bin/env python3
"""
Update discover_tokens.json with live data from the optimized data pipeline
"""

import asyncio
import sys
import os
import logging

# Now in backend directory - direct imports
from optimized_data_pipeline import process_top_tokens, process_spike_tokens

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def update_discover_tokens():
    """Update discover_tokens.json with live data"""
    try:
        logger.info("🚀 Starting live data update for discover_tokens.json")

        # Process top tokens (by volume/activity)
        logger.info("📊 Processing top tokens...")
        top_results = await process_top_tokens(limit=10)

        # Process spike tokens (volume spikes)
        logger.info("📈 Processing spike tokens...")
        spike_results = await process_spike_tokens(limit=5)

        total_processed = len(top_results) + len(spike_results)
        successful = sum(1 for r in top_results + spike_results if r.success)

        logger.info(f"✅ Live data update complete!")
        logger.info(f"📊 Processed {total_processed} tokens, {successful} successful")
        logger.info(f"💾 discover_tokens.json has been updated with live data")

        return {
            "success": True,
            "total_processed": total_processed,
            "successful": successful,
            "top_tokens": len(top_results),
            "spike_tokens": len(spike_results),
        }

    except Exception as e:
        logger.error(f"❌ Failed to update discover_tokens.json: {e}")
        return {"success": False, "error": str(e)}


def main():
    """Main function"""
    result = asyncio.run(update_discover_tokens())

    if result["success"]:
        print(f"\n🎉 Successfully updated discover_tokens.json with live data!")
        print(f"📊 Total tokens processed: {result['total_processed']}")
        print(f"✅ Successful: {result['successful']}")
        exit(0)
    else:
        print(f"\n❌ Failed to update discover_tokens.json: {result['error']}")
        exit(1)


if __name__ == "__main__":
    main()

"""
Manual Trading API - Execute trades based on user commands
Supports natural language trading commands like "buy 5 USDT of BTC"
"""

import re
from datetime import datetime
from typing import Dict, Any

# Flask imports with fallback stubs
try:
    from flask import Blueprint, request, jsonify  # type: ignore

    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    Blueprint = None  # type: ignore
    request = None  # type: ignore
    jsonify = None  # type: ignore

    class Blueprint:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def route(self, *args, **kwargs):
            def decorator(func):
                return func

            return decorator

    class Request:  # type: ignore
        def get_json(self):
            return {}

    def jsonify(data):  # type: ignore
        return data, 200

    request = Request()

# KuCoin SDK imports with fallback stubs
try:
    from kucoin.client import Trade, User, Market  # type: ignore

    KUCOIN_SDK_AVAILABLE = True
except ImportError:
    KUCOIN_SDK_AVAILABLE = False
    Trade = None  # type: ignore
    User = None  # type: ignore
    Market = None  # type: ignore

    class Trade:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def create_market_order(self, *args, **kwargs):
            return {"orderId": "stub_order_id"}

        def create_limit_order(self, *args, **kwargs):
            return {"orderId": "stub_order_id"}

        def cancel_order(self, *args, **kwargs):
            return {"cancelledOrderIds": []}

    class User:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def get_account_list(self, *args, **kwargs):
            return []

        def get_account(self, *args, **kwargs):
            return {"balance": "0", "available": "0", "holds": "0"}

    class Market:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def get_ticker(self, *args, **kwargs):
            return {"price": "0", "size": "0"}

        def get_24hr_stats(self, *args, **kwargs):
            return {"last": "0", "vol": "0"}

        def get_all_tickers(self, *args, **kwargs):
            return {"ticker": []}

        def create_market_order(self, **kwargs):
            return {"orderId": "stub_order_id"}

    class User:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def get_account_list(self):
            return []

    class Market:  # type: ignore
        def __init__(self, *args, **kwargs):
            pass

        def get_24hr_stats(self, symbol: str):
            return {"last": "0", "changeRate": "0", "vol": "0"}


import os
from dotenv import load_dotenv

load_dotenv()

# Initialize KuCoin clients
KUCOIN_API_KEY = os.getenv("KUCOIN_API_KEY")
KUCOIN_API_SECRET = os.getenv("KUCOIN_API_SECRET")
KUCOIN_API_PASSPHRASE = os.getenv("KUCOIN_API_PASSPHRASE")

manual_trading_bp = (
    Blueprint("manual_trading", __name__) if Blueprint is not None else None
)


class ManualTradingEngine:
    """Engine for processing manual trading commands"""

    def __init__(self):
        self.trade_client = None
        self.user_client = None
        self.market_client = None
        self.initialize_clients()

    def initialize_clients(self):
        """Initialize KuCoin clients with proper working setup"""
        try:
            # Migration SDK not available, fallback to standard KuCoin SDK initialization
            pass

        except Exception as e:
            print(f"⚠️ KuCoin SDK migration failed: {e}")

        # Fallback to direct initialization
        try:
            if (
                all([KUCOIN_API_KEY, KUCOIN_API_SECRET, KUCOIN_API_PASSPHRASE])
                and Trade is not None
                and User is not None
                and Market is not None
            ):
                self.trade_client = Trade(
                    key=KUCOIN_API_KEY,
                    secret=KUCOIN_API_SECRET,
                    passphrase=KUCOIN_API_PASSPHRASE,
                )
                self.user_client = User(
                    key=KUCOIN_API_KEY,
                    secret=KUCOIN_API_SECRET,
                    passphrase=KUCOIN_API_PASSPHRASE,
                )
                self.market_client = Market()
                print("✅ KuCoin clients initialized for manual trading")
            else:
                print("❌ KuCoin credentials missing or SDK not available")
                # Set to None so we can detect and provide better error messages
                self.trade_client = None
                self.user_client = None
                self.market_client = None
        except Exception as e:
            print(f"❌ Failed to initialize KuCoin clients: {e}")
            self.trade_client = None
            self.user_client = None
            self.market_client = None

    def parse_trading_command(self, command: str) -> Dict[str, Any]:
        """Parse natural language trading commands"""
        command = command.lower().strip()

        # Patterns for different command types
        patterns = {
            # "buy 5 usdt of btc" or "buy 5 dollars of bitcoin"
            "buy_amount": r"buy\s+(\d+(?:\.\d+)?)\s+(?:usdt|dollars?|usd|\$)\s+(?:of\s+)?(\w+)",
            # "buy 0.001 btc" or "buy 0.5 ethereum"
            "buy_quantity": r"buy\s+(\d+(?:\.\d+)?)\s+(\w+)",
            # "sell all btc" or "sell 50% of ethereum"
            "sell_all": r"sell\s+(?:all|100%)\s+(\w+)",
            "sell_percent": r"sell\s+(\d+)%\s+(?:of\s+)?(\w+)",
            "sell_quantity": r"sell\s+(\d+(?:\.\d+)?)\s+(\w+)",
            # "get balance" or "show portfolio"
            "balance": r"(?:get\s+)?(?:balance|portfolio|holdings)",
            # "price of btc" or "btc price"
            "price": r"(?:price\s+of\s+|(\w+)\s+price)(\w+)?",
        }

        for pattern_type, pattern in patterns.items():
            match = re.search(pattern, command)
            if match:
                if pattern_type == "buy_amount":
                    amount, symbol = match.groups()
                    return {
                        "action": "buy",
                        "type": "amount",
                        "amount": float(amount),
                        "symbol": self.normalize_symbol(symbol),
                        "original_command": command,
                    }
                elif pattern_type == "buy_quantity":
                    quantity, symbol = match.groups()
                    return {
                        "action": "buy",
                        "type": "quantity",
                        "quantity": float(quantity),
                        "symbol": self.normalize_symbol(symbol),
                        "original_command": command,
                    }
                elif pattern_type == "sell_all":
                    symbol = match.group(1)
                    return {
                        "action": "sell",
                        "type": "all",
                        "symbol": self.normalize_symbol(symbol),
                        "original_command": command,
                    }
                elif pattern_type == "sell_percent":
                    percent, symbol = match.groups()
                    return {
                        "action": "sell",
                        "type": "percent",
                        "percent": float(percent),
                        "symbol": self.normalize_symbol(symbol),
                        "original_command": command,
                    }
                elif pattern_type == "sell_quantity":
                    quantity, symbol = match.groups()
                    return {
                        "action": "sell",
                        "type": "quantity",
                        "quantity": float(quantity),
                        "symbol": self.normalize_symbol(symbol),
                        "original_command": command,
                    }
                elif pattern_type == "balance":
                    return {"action": "balance", "original_command": command}
                elif pattern_type == "price":
                    symbol = match.group(1) or match.group(2)
                    return {
                        "action": "price",
                        "symbol": self.normalize_symbol(symbol) if symbol else "BTC",
                        "original_command": command,
                    }

        return {
            "action": "unknown",
            "error": "Could not parse command",
            "original_command": command,
        }

    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to KuCoin format"""
        symbol = symbol.upper()

        # Common symbol mappings
        mappings = {
            "BITCOIN": "BTC",
            "ETHEREUM": "ETH",
            "CARDANO": "ADA",
            "POLKADOT": "DOT",
            "CHAINLINK": "LINK",
            "LITECOIN": "LTC",
            "RIPPLE": "XRP",
            "STELLAR": "XLM",
            "DOGECOIN": "DOGE",
            "SHIBA": "SHIB",
        }

        symbol = mappings.get(symbol, symbol)

        # Don't add -USDT to USDT itself or if already has -USDT
        if symbol == "USDT":
            return "USDT"  # Return as base currency for balance checks
        elif not symbol.endswith("-USDT"):
            symbol = f"{symbol}-USDT"

        return symbol

    async def execute_command(self, parsed_command: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the parsed trading command"""
        try:
            action = parsed_command["action"]

            if action == "buy":
                return await self.execute_buy_order(parsed_command)
            elif action == "sell":
                return await self.execute_sell_order(parsed_command)
            elif action == "balance":
                return await self.get_account_balance()
            elif action == "price":
                return await self.get_token_price(parsed_command["symbol"])
            else:
                return {
                    "success": False,
                    "error": f"Unknown action: {action}",
                    "command": parsed_command["original_command"],
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": parsed_command.get("original_command", "Unknown"),
            }

    async def execute_buy_order(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """Execute buy order"""
        if self.trade_client is None:
            return {
                "success": False,
                "error": "Trading client not available",
                "message": "KuCoin SDK not properly initialized",
            }

        try:
            symbol = command["symbol"]

            if command["type"] == "amount":
                # Buy with specific USDT amount
                funds = str(command["amount"])

                order_result = self.trade_client.create_market_order(
                    symbol=symbol, side="buy", funds=funds
                )

                return {
                    "success": True,
                    "action": "buy",
                    "order_id": order_result.get("orderId"),
                    "symbol": symbol,
                    "funds_used": command["amount"],
                    "type": "market",
                    "message": f"Successfully bought {symbol} with ${command['amount']} USDT",
                    "timestamp": datetime.now().isoformat(),
                }

            elif command["type"] == "quantity":
                # Buy specific quantity
                size = str(command["quantity"])

                order_result = self.trade_client.create_market_order(
                    symbol=symbol, side="buy", size=size
                )

                return {
                    "success": True,
                    "action": "buy",
                    "order_id": order_result.get("orderId"),
                    "symbol": symbol,
                    "quantity": command["quantity"],
                    "type": "market",
                    "message": f"Successfully bought {command['quantity']} {symbol}",
                    "timestamp": datetime.now().isoformat(),
                }

            # Default fallback for unknown command types
            return {
                "success": False,
                "error": f"Unknown command type: {command.get('type', 'unknown')}",
                "action": "buy",
                "symbol": command.get("symbol", "unknown"),
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "action": "buy",
                "symbol": command.get("symbol", "unknown"),
            }

    async def execute_sell_order(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """Execute sell order"""
        if self.trade_client is None or self.user_client is None:
            return {
                "success": False,
                "error": "Trading client not available",
                "message": "KuCoin SDK not properly initialized",
            }

        try:
            symbol = command["symbol"]

            # Handle special case for USDT (can't sell USDT directly)
            if symbol == "USDT":
                return {
                    "success": False,
                    "error": "Cannot sell USDT directly. USDT is the quote currency.",
                    "action": "sell",
                    "symbol": symbol,
                    "suggestion": "Try selling other cryptocurrencies to get USDT",
                }

            # Extract base currency from trading pair
            if "-" in symbol:
                base_currency = symbol.split("-")[0]
            else:
                base_currency = symbol
                symbol = f"{symbol}-USDT"  # Ensure proper trading pair format

            # Get current balance
            accounts = self.user_client.get_account_list()
            available_balance = 0

            for account in accounts:
                if account["currency"] == base_currency and account["type"] == "trade":
                    available_balance = float(account["available"])
                    break

            if available_balance <= 0:
                return {
                    "success": False,
                    "error": f"No {base_currency} balance available to sell",
                    "action": "sell",
                    "symbol": symbol,
                    "available_balance": available_balance,
                }

            size = "0"  # Initialize size variable
            if command["type"] == "all":
                # Sell all available balance
                size = str(available_balance)
            elif command["type"] == "percent":
                # Sell percentage of balance
                sell_amount = available_balance * (command["percent"] / 100)
                size = str(sell_amount)
            elif command["type"] == "quantity":
                # Sell specific quantity
                if command["quantity"] > available_balance:
                    return {
                        "success": False,
                        "error": f"Insufficient balance. Available: {available_balance}, Requested: {command['quantity']}",
                        "action": "sell",
                        "symbol": symbol,
                    }
                size = str(command["quantity"])

            order_result = self.trade_client.create_market_order(
                symbol=symbol, side="sell", size=size
            )

            return {
                "success": True,
                "action": "sell",
                "order_id": order_result.get("orderId"),
                "symbol": symbol,
                "quantity_sold": float(size),
                "type": "market",
                "message": f"Successfully sold {size} {base_currency}",
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "action": "sell",
                "symbol": command.get("symbol", "unknown"),
            }

    async def get_account_balance(self) -> Dict[str, Any]:
        """Get account balance"""
        if self.user_client is None:
            return {
                "success": False,
                "error": "User client not available",
                "message": "KuCoin SDK not properly initialized",
            }

        try:
            accounts = self.user_client.get_account_list()
            balances = {}
            total_usdt_value = 0

            for account in accounts:
                if account["type"] == "trade" and float(account["available"]) > 0:
                    currency = account["currency"]
                    available = float(account["available"])
                    balances[currency] = {
                        "available": available,
                        "hold": float(account["holds"]),
                    }

                    # Estimate USDT value (simplified)
                    if currency == "USDT":
                        total_usdt_value += available
                    else:
                        # Would need price conversion for accurate value
                        pass

            return {
                "success": True,
                "action": "balance",
                "balances": balances,
                "estimated_total_usdt": total_usdt_value,
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {"success": False, "error": str(e), "action": "balance"}

    async def get_token_price(self, symbol: str) -> Dict[str, Any]:
        """Get current token price"""
        if self.market_client is None:
            return {
                "success": False,
                "error": "Market client not available",
                "message": "KuCoin SDK not properly initialized",
            }

        try:
            # Handle special case for USDT
            if symbol == "USDT":
                return {
                    "success": True,
                    "action": "price",
                    "symbol": symbol,
                    "price": 1.0,
                    "change_24h": 0.0,
                    "volume_24h": 0.0,
                    "timestamp": datetime.now().isoformat(),
                    "note": "USDT is a stablecoin pegged to $1 USD",
                }

            # Ensure proper trading pair format
            if not symbol.endswith("-USDT"):
                symbol = f"{symbol}-USDT"

            ticker = self.market_client.get_24hr_stats(symbol)

            return {
                "success": True,
                "action": "price",
                "symbol": symbol,
                "price": float(ticker["last"]),
                "change_24h": float(ticker["changeRate"]) * 100,
                "volume_24h": float(ticker["vol"]),
                "timestamp": datetime.now().isoformat(),
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "action": "price",
                "symbol": symbol,
            }


# Initialize trading engine
trading_engine = ManualTradingEngine()


if manual_trading_bp is not None:

    @manual_trading_bp.route("/execute-command", methods=["POST"])
    async def execute_trading_command():
        """Execute a natural language trading command"""
        if request is None or jsonify is None:
            return {"success": False, "error": "Flask not available"}, 500

        try:
            data = request.get_json()
            command = data.get("command", "").strip()

            if not command:
                return jsonify({"success": False, "error": "No command provided"}), 400

            # Parse the command
            parsed_command = trading_engine.parse_trading_command(command)

            if parsed_command["action"] == "unknown":
                return (
                    jsonify(
                        {
                            "success": False,
                            "error": "Could not understand command",
                            "parsed_command": parsed_command,
                            "suggestions": [
                                "buy 5 usdt of btc",
                                "sell all eth",
                                "get balance",
                                "btc price",
                            ],
                        }
                    ),
                    400,
                )

            # Execute the command
            result = await trading_engine.execute_command(parsed_command)

            return jsonify(
                {
                    "success": result.get("success", False),
                    "result": result,
                    "parsed_command": parsed_command,
                    "original_command": command,
                }
            )

        except Exception as e:
            return jsonify({"success": False, "error": str(e)}), 500

    @manual_trading_bp.route("/parse-command", methods=["POST"])
    def parse_trading_command():
        """Parse a trading command without executing it"""
        if request is None or jsonify is None:
            return {"success": False, "error": "Flask not available"}, 500

        try:
            data = request.get_json()
            command = data.get("command", "").strip()

            if not command:
                return jsonify({"success": False, "error": "No command provided"}), 400

            parsed_command = trading_engine.parse_trading_command(command)

            return jsonify(
                {
                    "success": True,
                    "parsed_command": parsed_command,
                    "original_command": command,
                }
            )

        except Exception as e:
            return jsonify({"success": False, "error": str(e)}), 500

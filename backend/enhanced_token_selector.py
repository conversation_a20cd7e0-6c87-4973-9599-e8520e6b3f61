"""
Enhanced Token Selector with Advanced Discovery Logic
Improved scoring algorithm, better filtering, and dynamic token selection
"""

import json
import logging
import os

# import asyncio  # Not used
from typing import List, Dict
from datetime import datetime

# import statistics  # Not used
from dataclasses import dataclass

# SDK-based imports for optimal performance
from kucoin_sdk_migration import kucoin_sdk
from coingecko_sdk_migration import coingecko_sdk

# from ai_clients_sdk_migration import get_ai_clients_status  # Not used

# from news_sentiment_fallback import get_combined_sentiment_score  # Not used
from price_fetcher import get_price
from config import COINGECKO_API_KEY
from kucoin_data import fetch_kucoin_spike_tokens
from cache import get_cached_data, set_cached_data

# Import new robust systems
ROBUST_SYSTEMS_ERROR = None
try:
    from sentiment_fallback_system import get_robust_sentiment_score

    # from github_rate_limiter import get_github_sentiment_safe  # Used conditionally
    # from api_polling_optimizer import record_success, record_error  # Used conditionally

    ROBUST_SYSTEMS_AVAILABLE = True
except ImportError as e:
    # Logger will be defined later, so we'll set this flag and log later
    ROBUST_SYSTEMS_AVAILABLE = False
    ROBUST_SYSTEMS_ERROR = str(e)

    # Fallback functions
    def get_robust_sentiment_score(token: str, news_items=None) -> float:
        """Fallback sentiment function"""
        return 0.5

    def get_github_sentiment_safe(token: str) -> float:
        """Fallback GitHub sentiment function"""
        return 0.5

    def record_success(endpoint: str):
        """Fallback success recording"""
        pass

    def record_error(endpoint: str, error):
        """Fallback error recording"""
        pass


# Enhanced Configuration
@dataclass
class TokenSelectorConfig:
    # Dynamic limits based on market conditions
    MIN_TOKENS = 10
    MAX_TOKENS = 50
    DEFAULT_TOKENS = 20

    # Volume thresholds (USD) - RELAXED for more tokens
    MIN_VOLUME_THRESHOLD = 5_000  # 5K minimum (much lower)
    HIGH_VOLUME_THRESHOLD = 1_000_000  # 1M for high volume bonus

    # Price change thresholds - RELAXED
    MIN_PRICE_CHANGE = -0.50  # -50% minimum (allow bigger dumps)
    HIGH_GAIN_THRESHOLD = 0.05  # 5%+ for momentum bonus

    # Market cap thresholds (USD) - RELAXED
    MIN_MARKET_CAP = 10_000  # 10K minimum (much lower)
    MID_CAP_THRESHOLD = 10_000_000  # 10M
    LARGE_CAP_THRESHOLD = 100_000_000  # 100M

    # Scoring weights (must sum to 1.0)
    WEIGHTS = {
        "volume": 0.25,
        "price_momentum": 0.20,
        "market_cap": 0.15,
        "sentiment": 0.20,
        "volatility": 0.10,
        "spike_ratio": 0.10,
    }


config = TokenSelectorConfig()

# URLs and file paths
KUCOIN_URL = "https://api.kucoin.com/api/v1/market/allTickers"
KUCOIN_24HR_URL = "https://api.kucoin.com/api/v1/market/stats"
COINGECKO_URL = "https://api.coingecko.com/api/v3/coins/markets"
DISCOVER_FILE = "backend/data/enhanced_discover_tokens.json"
CACHE_FILE = "backend/data/enhanced_token_cache.json"
LOG_FILE = "backend/logs/enhanced_token_selector.log"

# Setup logging
os.makedirs("backend/logs", exist_ok=True)
logging.basicConfig(
    filename=LOG_FILE,
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Log robust systems status after logger is defined
if not ROBUST_SYSTEMS_AVAILABLE:
    logger.warning(f"Robust systems not available: {ROBUST_SYSTEMS_ERROR}")
else:
    logger.info(
        "✅ Robust systems (sentiment, GitHub, API optimizer) loaded successfully"
    )


class EnhancedTokenSelector:
    def __init__(self):
        self.market_data_cache = {}
        self.sentiment_cache = {}

    def _is_valid_token(self, symbol: str, token: Dict) -> bool:
        """
        Enhanced token validation with multiple criteria
        """
        try:
            # Basic symbol validation
            if not symbol.endswith("-USDT"):
                return False

            # Skip leveraged and derivative tokens
            skip_patterns = [
                "3L-",
                "3S-",
                "UP-",
                "DOWN-",
                "BEAR",
                "BULL",
                "LEVERAGED",
                "SHORT",
                "LONG",
                "2X-",
                "3X-",
            ]
            if any(pattern in symbol for pattern in skip_patterns):
                return False

            # Volume validation
            volume = float(token.get("volValue", 0))
            if volume < config.MIN_VOLUME_THRESHOLD:
                return False

            # Price validation
            price = float(token.get("last", 0))
            if price <= 0:
                return False

            # Price change validation (avoid major dumps)
            change_rate = float(token.get("changeRate", 0))
            if change_rate < config.MIN_PRICE_CHANGE:
                return False

            return True

        except (ValueError, TypeError) as e:
            logger.warning(f"Token validation error for {symbol}: {e}")
            return False

    async def _add_calculated_metrics(self, token: Dict) -> Dict:
        """
        Add calculated metrics to token data
        """
        try:
            symbol = token["symbol"]

            # Calculate volatility (using high/low spread)
            high = float(token.get("high", 0))
            low = float(token.get("low", 0))
            last = float(token.get("last", 0))

            if high > 0 and low > 0 and last > 0:
                volatility = (high - low) / last
                token["volatility"] = volatility
            else:
                token["volatility"] = 0

            # Calculate momentum score
            change_rate = float(token.get("changeRate", 0))
            volume = float(token.get("volValue", 0))

            # Momentum = price change * volume weight
            volume_weight = min(volume / config.HIGH_VOLUME_THRESHOLD, 2.0)  # Cap at 2x
            token["momentum_score"] = change_rate * volume_weight

            # Add price fetch
            try:
                price = await get_price(symbol)
                if price:
                    token["current_price"] = price
            except Exception as e:
                logger.warning(f"Price fetch failed for {symbol}: {e}")
                token["current_price"] = token.get("last", 0)

            return token

        except Exception as e:
            logger.error(
                f"Error adding calculated metrics for {token.get('symbol', 'unknown')}: {e}"
            )
            return token

    async def fetch_comprehensive_kucoin_data(self) -> List[Dict]:
        """
        Fetch comprehensive KuCoin market data with 24hr statistics
        """
        try:
            logger.info("Fetching comprehensive KuCoin market data...")

            # Fetch all tickers using KuCoin SDK
            raw_tokens = kucoin_sdk.get_all_tickers()

            # Fetch 24hr stats for additional metrics with improved error handling
            stats_data = {}
            try:
                # Use KuCoin SDK for 24hr stats (already included in get_all_tickers)
                logger.info("Using KuCoin SDK for 24hr stats...")
                # Stats are already included in the tickers from SDK
                stats_available = True

                if stats_available:
                    # Stats are already included in the tickers from SDK
                    stats_data = {token["symbol"]: token for token in raw_tokens}
                    logger.info(
                        f"✅ Successfully using SDK stats for {len(stats_data)} tokens"
                    )
                else:
                    logger.warning("KuCoin SDK stats not available")

            except Exception as e:
                logger.warning(f"Failed to fetch 24hr stats, using fallback: {e}")
                # Continue without 24hr stats - the system should still work

            enhanced_tokens = []

            for token in raw_tokens:
                symbol = token.get("symbol", "").upper()

                # Enhanced filtering
                if not self._is_valid_token(symbol, token):
                    continue

                # Enrich with 24hr stats
                stats = stats_data.get(symbol, {})
                enhanced_token = {**token, **stats}
                enhanced_token["symbol"] = symbol

                # Add calculated metrics
                enhanced_token = await self._add_calculated_metrics(enhanced_token)

                enhanced_tokens.append(enhanced_token)

                # Dynamic limit based on quality
                if (
                    len(enhanced_tokens) >= config.MAX_TOKENS * 2
                ):  # Fetch more for better selection
                    break

            logger.info(f"Fetched {len(enhanced_tokens)} enhanced tokens")
            return enhanced_tokens

        except Exception as e:
            logger.error(f"Error fetching comprehensive KuCoin data: {e}")
            return []

    async def fetch_market_cap_data(self) -> Dict[str, Dict]:
        """
        Fetch market cap data from CoinGecko
        """
        try:
            logger.info("Fetching CoinGecko market cap data...")
            headers = (
                {"x-cg-pro-api-key": COINGECKO_API_KEY} if COINGECKO_API_KEY else {}
            )

            # Fetch top 1000 coins by market cap using CoinGecko SDK
            logger.info("Fetching CoinGecko market data using SDK...")
            market_data_list = coingecko_sdk.get_coins_markets(limit=1000)

            market_data = {}
            for coin in market_data_list:
                symbol = coin.get("symbol", "").upper()
                market_data[symbol] = {
                    "market_cap": coin.get("market_cap", 0),
                    "market_cap_rank": coin.get("market_cap_rank", 999999),
                    "price_change_24h": coin.get("price_change_percentage_24h", 0),
                    "volume_24h": coin.get("total_volume", 0),
                }

            return market_data

        except Exception as e:
            logger.error(f"Error fetching market cap data: {e}")
            return {}

    async def build_sentiment_scores(self, tokens: List[Dict]) -> Dict[str, float]:
        """
        Build sentiment scores for tokens with caching
        """
        logger.info("Building sentiment scores...")
        scores = {}

        for i, token in enumerate(tokens):
            symbol = token["symbol"]
            base_token = symbol.replace("-USDT", "")

            # Check cache first
            if base_token in self.sentiment_cache:
                scores[symbol] = self.sentiment_cache[base_token]
                continue

            try:
                # Create a mock news item for sentiment analysis
                mock_news_item = {
                    "title": f"{base_token} cryptocurrency analysis",
                    "description": f"Market analysis for {base_token} token",
                }

                # Use robust sentiment scoring system with fallbacks
                if ROBUST_SYSTEMS_AVAILABLE:
                    try:
                        final_score = get_robust_sentiment_score(base_token)
                        logger.debug(
                            f"✅ Robust sentiment for {base_token}: {final_score:.3f}"
                        )
                    except Exception as e:
                        logger.warning(f"Robust sentiment failed for {base_token}: {e}")
                        final_score = self._fallback_sentiment_score(base_token)
                else:
                    final_score = self._fallback_sentiment_score(base_token)

                scores[symbol] = final_score
                self.sentiment_cache[base_token] = final_score

                logger.info(
                    f"  → [{i+1}/{len(tokens)}] {symbol}: {final_score:.3f} (robust sentiment)"
                )

            except Exception as e:
                logger.warning(f"Sentiment fetch failed for {symbol}: {e}")
                scores[symbol] = 0.5  # Default to neutral instead of 0

        return scores

    def _fallback_sentiment_score(self, base_token: str) -> float:
        """Fallback sentiment scoring when robust systems are unavailable."""
        from news_sentiment_fallback import simple_sentiment_score
        import hashlib

        hash_val = int(hashlib.md5(base_token.encode()).hexdigest()[:8], 16)

        # Create varied sentiment scenarios
        sentiment_scenarios = [
            f"{base_token} showing strong bullish momentum with rising volume",
            f"{base_token} market consolidation phase with mixed signals",
            f"{base_token} experiencing bearish pressure amid market uncertainty",
            f"{base_token} neutral trading range with stable fundamentals",
            f"{base_token} positive development news driving investor interest",
        ]

        scenario_index = hash_val % len(sentiment_scenarios)
        sentiment_text = sentiment_scenarios[scenario_index]
        score = simple_sentiment_score(sentiment_text)

        # Convert from -1,1 range to 0,1 range for display
        normalized_score = (score + 1) / 2  # Convert -1,1 to 0,1

        # Add small consistent variation based on token (not random each time)
        token_factor = (hash_val % 200) / 1000.0  # 0.0 to 0.199
        final_score = max(
            0.05, min(0.95, normalized_score + token_factor - 0.1)
        )  # Center around 0.5

        return final_score

    def calculate_enhanced_score(
        self, token: Dict, market_data: Dict, sentiment_scores: Dict, spike_ratios: Dict
    ) -> float:
        """
        Calculate enhanced composite score using multiple factors
        """
        try:
            symbol = token["symbol"]
            base_symbol = symbol.replace("-USDT", "")

            # Extract metrics
            volume = float(token.get("volValue", 0))
            change_rate = float(token.get("changeRate", 0))
            volatility = float(token.get("volatility", 0))
            momentum_score = float(token.get("momentum_score", 0))

            # Volume score (normalized)
            volume_score = min(volume / config.HIGH_VOLUME_THRESHOLD, 2.0)

            # Price momentum score
            momentum_multiplier = (
                1.5 if change_rate > config.HIGH_GAIN_THRESHOLD else 1.0
            )
            price_momentum_score = change_rate * momentum_multiplier

            # Market cap score
            market_info = market_data.get(base_symbol, {})
            market_cap = float(market_info.get("market_cap", 0))
            if market_cap > config.LARGE_CAP_THRESHOLD:
                mc_score = 3.0  # Large cap bonus
            elif market_cap > config.MID_CAP_THRESHOLD:
                mc_score = 2.0  # Mid cap
            elif market_cap > config.MIN_MARKET_CAP:
                mc_score = 1.0  # Small cap
            else:
                mc_score = 0.5  # Micro cap penalty

            # Sentiment score
            sentiment_score = sentiment_scores.get(symbol, 0.0)

            # Volatility score (moderate volatility preferred)
            volatility_score = min(volatility * 10, 2.0) if volatility > 0 else 0

            # Spike ratio score
            spike_score = min(spike_ratios.get(symbol, 1.0) - 1.0, 2.0)

            # Calculate weighted composite score
            composite_score = (
                volume_score * config.WEIGHTS["volume"]
                + price_momentum_score * config.WEIGHTS["price_momentum"]
                + mc_score * config.WEIGHTS["market_cap"]
                + sentiment_score * config.WEIGHTS["sentiment"]
                + volatility_score * config.WEIGHTS["volatility"]
                + spike_score * config.WEIGHTS["spike_ratio"]
            )

            return round(composite_score, 4)

        except Exception as e:
            logger.error(
                f"Error calculating score for {token.get('symbol', 'unknown')}: {e}"
            )
            return 0.0

    async def generate_enhanced_token_list(self, limit: int = 20) -> List[Dict]:
        """
        Generate enhanced token list with improved scoring and filtering
        """
        if limit <= 0:
            limit = config.DEFAULT_TOKENS

        cache_key = f"enhanced_tokens_{limit}"
        cached_data = get_cached_data(cache_key)
        if cached_data:
            logger.info(f"Returning cached enhanced tokens for limit {limit}")
            return cached_data

        try:
            logger.info("Generating enhanced token list...")

            # Fetch comprehensive data
            tokens = await self.fetch_comprehensive_kucoin_data()
            if not tokens:
                logger.warning("No tokens fetched from KuCoin")
                return []

            # Fetch market cap data
            market_data = await self.fetch_market_cap_data()

            # Build sentiment scores
            sentiment_scores = await self.build_sentiment_scores(tokens)

            # Get spike ratios
            try:
                spike_data = fetch_kucoin_spike_tokens()
                spike_ratios = {
                    entry["symbol"]: entry.get(
                        "multiplier", entry.get("volume_ratio", 1.0)
                    )
                    for entry in spike_data
                    if "symbol" in entry
                }
            except Exception as e:
                logger.warning(f"Failed to fetch spike data: {e}")
                spike_ratios = {}

            # Calculate enhanced scores
            scored_tokens = []
            for token in tokens:
                score = self.calculate_enhanced_score(
                    token, market_data, sentiment_scores, spike_ratios
                )

                if score > 0:  # Only include tokens with positive scores
                    enhanced_token = {
                        "symbol": token["symbol"],
                        "price": token.get("current_price", token.get("last", 0)),
                        "score": score,
                        "volume": float(token.get("volValue", 0)),
                        "change_24h": float(token.get("changeRate", 0)),
                        "volatility": float(token.get("volatility", 0)),
                        "sentiment": sentiment_scores.get(token["symbol"], 0.0),
                        "market_cap": market_data.get(
                            token["symbol"].replace("-USDT", ""), {}
                        ).get("market_cap", 0),
                        "spike_ratio": spike_ratios.get(token["symbol"], 1.0),
                        "timestamp": datetime.now().isoformat(),
                        "source": "enhanced_kucoin_data",
                    }
                    scored_tokens.append(enhanced_token)

            # Sort by score descending
            sorted_tokens = sorted(
                scored_tokens, key=lambda x: x["score"], reverse=True
            )

            # Apply dynamic limit based on score distribution
            if len(sorted_tokens) > limit:
                # Use score threshold to ensure quality
                score_threshold = (
                    sorted_tokens[limit - 1]["score"] * 0.8
                )  # 80% of the limit-th score
                final_tokens = [
                    token
                    for token in sorted_tokens
                    if token["score"] >= score_threshold
                ][:limit]
            else:
                final_tokens = sorted_tokens

            # Save to cache and file
            set_cached_data(cache_key, final_tokens, ttl=300)  # Cache for 5 minutes

            # Save to file
            try:
                os.makedirs(os.path.dirname(DISCOVER_FILE), exist_ok=True)
                with open(DISCOVER_FILE, "w") as f:
                    json.dump(final_tokens, f, indent=2)
                logger.info(
                    f"Saved {len(final_tokens)} enhanced tokens to {DISCOVER_FILE}"
                )
            except Exception as e:
                logger.error(f"Failed to save enhanced tokens to file: {e}")

            logger.info(
                f"Generated {len(final_tokens)} enhanced tokens with scores ranging from "
                f"{final_tokens[-1]['score']:.4f} to {final_tokens[0]['score']:.4f}"
            )

            return final_tokens

        except Exception as e:
            logger.error(f"Error generating enhanced token list: {e}")
            return []


# Global instance
enhanced_selector = EnhancedTokenSelector()


# Public API functions for backward compatibility
async def generate_enhanced_top_tokens(limit: int = 20) -> List[Dict]:
    """
    Generate enhanced top tokens list
    """
    return await enhanced_selector.generate_enhanced_token_list(limit)


# Optimized helper functions for rate-limited API calls


def _get_kucoin_tokens_with_rate_limit() -> List[Dict]:
    """Get KuCoin tokens with rate limiting and error handling"""
    try:
        import time
        from kucoin_data import fetch_kucoin_spike_tokens

        # Add small delay to prevent rate limiting
        time.sleep(0.5)

        tokens = fetch_kucoin_spike_tokens()
        if tokens:
            logger.info(f"✅ Successfully fetched {len(tokens)} KuCoin tokens")
            return tokens
        else:
            logger.warning("No tokens returned from KuCoin API")
            return []

    except Exception as e:
        logger.error(f"KuCoin API error: {e}")
        return []


def _pre_filter_tokens(tokens: List[Dict], limit: int) -> List[Dict]:
    """Pre-filter tokens based on volume, market cap, and basic criteria"""
    try:
        filtered = []

        logger.info(
            f"🔍 Pre-filtering {len(tokens)} tokens with FIXED criteria for KuCoin data"
        )

        for token in tokens:
            # Basic validation
            if not token.get("symbol") or not token.get("price"):
                continue

            # Skip non-USDT pairs for now (focus on USDT pairs)
            symbol = token.get("symbol", "")
            if not symbol.endswith("-USDT"):
                continue

            # Handle different KuCoin data structures
            # From kucoin_data: 'volume' is USD volume
            # From kucoin_sdk: 'volume_value' is USD volume, 'volume' is token volume
            volume_usd = float(token.get("volume_value", token.get("volume", 0)))

            # Use USD volume for filtering (more meaningful)
            if volume_usd < 100:  # Minimum $100 USD volume (very low threshold)
                continue

            # Price validation
            price = float(token.get("price", 0))
            if price <= 0:
                continue

            # Price change validation - handle different field names with None safety
            price_change_raw = token.get("change_24h", token.get("cg_change", 0))
            price_change = (
                float(price_change_raw) if price_change_raw is not None else 0.0
            )
            if (
                abs(price_change) > 1000
            ):  # Skip tokens with >1000% change (extreme outliers)
                continue

            # Calculate estimated market cap (rough estimate: volume * 10)
            estimated_market_cap = volume_usd * 10  # Conservative estimate

            # Normalize token data for consistent structure
            normalized_token = {
                "symbol": symbol,
                "name": symbol.replace("-USDT", ""),  # Clean symbol name
                "price": price,
                "volume_24h": volume_usd,  # Use USD volume
                "market_cap": estimated_market_cap,
                "price_change_24h": price_change,
                "volume_change_24h": float(
                    token.get("volume_ratio", 0)
                ),  # Volume ratio if available
                "is_new": abs(price_change)
                > 5,  # Consider >5% change as "new/trending"
                "source": "kucoin_fixed",
                "high_24h": float(token.get("high_24h", price)),
                "low_24h": float(token.get("low_24h", price)),
                "is_spike": token.get("is_spike", False),
            }

            filtered.append(normalized_token)

        logger.info(f"✅ Pre-filtering passed {len(filtered)} tokens (FIXED)")

        # Sort by USD volume and return top candidates
        filtered.sort(key=lambda x: float(x.get("volume_24h", 0)), reverse=True)
        return filtered[: limit * 2]  # Return more candidates for further filtering

    except Exception as e:
        logger.error(f"Error in pre-filtering tokens: {e}")
        return tokens[:limit]  # Return original tokens if filtering fails


def _enhance_with_coingecko(tokens: List[Dict]) -> List[Dict]:
    """Enhance tokens with CoinGecko data (rate-limited)"""
    try:
        import time

        enhanced_tokens = []

        for i, token in enumerate(tokens):
            # Rate limiting: 1 request per second for CoinGecko
            if i > 0:
                time.sleep(1.1)

            try:
                # Add basic CoinGecko enhancement (mock for now to avoid rate limits)
                enhanced_token = token.copy()
                enhanced_token["coingecko_enhanced"] = True
                enhanced_token["sentiment_score"] = (
                    0.5 + (hash(token["symbol"]) % 100) / 200
                )  # Mock sentiment
                enhanced_tokens.append(enhanced_token)

            except Exception as e:
                logger.warning(
                    f"Failed to enhance {token.get('symbol', 'unknown')} with CoinGecko: {e}"
                )
                enhanced_tokens.append(token)  # Add without enhancement

        return enhanced_tokens

    except Exception as e:
        logger.error(f"Error enhancing with CoinGecko: {e}")
        return tokens


def _enhance_with_priority_system(tokens: List[Dict]) -> List[Dict]:
    """
    Priority Enhancement System: TokenMetrics FIRST, then CoinGecko if needed
    Uses 200+ data points efficiently by prioritizing high-value APIs
    """
    try:
        import time

        enhanced_tokens = []

        logger.info(f"🎯 Starting priority enhancement for {len(tokens)} tokens")

        for i, token in enumerate(tokens):
            symbol = token.get("symbol", "").replace("-USDT", "")
            enhanced_token = token.copy()

            # Rate limiting between tokens
            if i > 0:
                time.sleep(0.5)  # Reduced delay for efficiency

            try:
                # PRIORITY 1: TokenMetrics (Advanced AI Analysis)
                tokenmetrics_data = _get_tokenmetrics_data(symbol)
                if tokenmetrics_data and tokenmetrics_data.get("available"):
                    enhanced_token.update(
                        {
                            "tokenmetrics_grade": tokenmetrics_data.get("grade", "N/A"),
                            "tokenmetrics_score": tokenmetrics_data.get("score", 0),
                            "tokenmetrics_confidence": tokenmetrics_data.get(
                                "confidence", 0
                            ),
                            "ai_recommendation": tokenmetrics_data.get(
                                "ai_analysis", {}
                            ).get("recommendation", "HOLD"),
                            "technical_score": tokenmetrics_data.get(
                                "technical_analysis", {}
                            ).get("score", 0.5),
                            "social_sentiment": tokenmetrics_data.get(
                                "social_sentiment", 0.5
                            ),
                            "liquidity_score": tokenmetrics_data.get(
                                "liquidity_score", 0.5
                            ),
                            "data_completeness": 0.9,  # High completeness with TokenMetrics
                            "enhanced_with": "tokenmetrics",
                        }
                    )

                    # If TokenMetrics provides sufficient data (90%+), skip CoinGecko
                    if tokenmetrics_data.get("confidence", 0) > 0.8:
                        enhanced_token["sentiment_score"] = tokenmetrics_data.get(
                            "social_sentiment", 0.5
                        )
                        enhanced_tokens.append(enhanced_token)
                        continue

                # PRIORITY 2: CoinGecko (Only if TokenMetrics insufficient)
                logger.debug(f"TokenMetrics insufficient for {symbol}, using CoinGecko")
                coingecko_data = _get_coingecko_data_minimal(symbol)
                if coingecko_data:
                    enhanced_token.update(
                        {
                            "cg_market_cap": coingecko_data.get("market_cap", 0),
                            "cg_volume": coingecko_data.get("volume", 0),
                            "cg_change": coingecko_data.get("price_change_24h", 0),
                            "sentiment_score": 0.5,  # Neutral sentiment from CoinGecko
                            "data_completeness": 0.6,  # Moderate completeness
                            "enhanced_with": "coingecko",
                        }
                    )
                else:
                    # FALLBACK: Basic enhancement
                    enhanced_token.update(
                        {
                            "sentiment_score": 0.5,
                            "data_completeness": 0.3,
                            "enhanced_with": "basic",
                        }
                    )

                enhanced_tokens.append(enhanced_token)

            except Exception as e:
                logger.warning(f"Failed to enhance {symbol}: {e}")
                enhanced_token.update(
                    {
                        "sentiment_score": 0.5,
                        "data_completeness": 0.2,
                        "enhanced_with": "fallback",
                    }
                )
                enhanced_tokens.append(enhanced_token)

        # Log enhancement statistics
        tokenmetrics_count = sum(
            1 for t in enhanced_tokens if t.get("enhanced_with") == "tokenmetrics"
        )
        coingecko_count = sum(
            1 for t in enhanced_tokens if t.get("enhanced_with") == "coingecko"
        )

        logger.info(
            f"📊 Enhancement complete: {tokenmetrics_count} TokenMetrics, {coingecko_count} CoinGecko"
        )

        return enhanced_tokens

    except Exception as e:
        logger.error(f"Error in priority enhancement system: {e}")
        return tokens


def _get_tokenmetrics_data(symbol: str) -> Dict:
    """Get TokenMetrics data with error handling"""
    try:
        from tokenmetrics_api import TokenMetricsAPI

        tm_api = TokenMetricsAPI()
        return tm_api.get_comprehensive_analysis(symbol)
    except Exception as e:
        logger.debug(f"TokenMetrics failed for {symbol}: {e}")
        return {}


def _get_coingecko_data_minimal(symbol: str) -> Dict:
    """Get minimal CoinGecko data with rate limiting"""
    try:
        import time

        time.sleep(1.1)  # CoinGecko rate limiting

        # Mock CoinGecko data for now (replace with actual API call)
        return {
            "market_cap": 1000000 + (hash(symbol) % 1000000),
            "volume": 100000 + (hash(symbol) % 100000),
            "price_change_24h": (hash(symbol) % 20) - 10,  # -10 to +10
        }
    except Exception as e:
        logger.debug(f"CoinGecko failed for {symbol}: {e}")
        return {}


def _calculate_final_scores(tokens: List[Dict]) -> List[Dict]:
    """Calculate final scores and sort tokens"""
    try:
        for token in tokens:
            # Calculate combined score
            volume_score = min(
                float(token.get("volume_24h", 0)) / 10000000, 1.0
            )  # Normalize to 1.0
            market_cap_score = min(
                float(token.get("market_cap", 0)) / 1000000000, 1.0
            )  # Normalize to 1.0
            sentiment_score = float(token.get("sentiment_score", 0.5))

            # Combined score with weights
            combined_score = (
                volume_score * 0.4 + market_cap_score * 0.3 + sentiment_score * 0.3
            )

            token["combined_score"] = combined_score

        # Sort by combined score
        tokens.sort(key=lambda x: x.get("combined_score", 0), reverse=True)
        return tokens

    except Exception as e:
        logger.error(f"Error calculating final scores: {e}")
        return tokens


def _get_fallback_tokens(limit: int) -> List[Dict]:
    """Get fallback tokens when APIs fail"""
    fallback_tokens = [
        {
            "symbol": "BTC",
            "name": "Bitcoin",
            "price": 50000,
            "volume_24h": 1000000000,
            "market_cap": 1000000000000,
            "combined_score": 0.9,
        },
        {
            "symbol": "ETH",
            "name": "Ethereum",
            "price": 3000,
            "volume_24h": 500000000,
            "market_cap": 400000000000,
            "combined_score": 0.8,
        },
        {
            "symbol": "BNB",
            "name": "Binance Coin",
            "price": 300,
            "volume_24h": 200000000,
            "market_cap": 50000000000,
            "combined_score": 0.7,
        },
        {
            "symbol": "SOL",
            "name": "Solana",
            "price": 100,
            "volume_24h": 150000000,
            "market_cap": 40000000000,
            "combined_score": 0.6,
        },
        {
            "symbol": "ADA",
            "name": "Cardano",
            "price": 0.5,
            "volume_24h": 100000000,
            "market_cap": 20000000000,
            "combined_score": 0.5,
        },
    ]

    logger.info(f"Using {len(fallback_tokens[:limit])} fallback tokens")
    return fallback_tokens[:limit]


def get_enhanced_discover_tokens(limit: int = 20) -> List[Dict]:
    """
    Optimized token selection flow: KuCoin → Filter → CoinGecko → TokenMetrics
    This prevents unnecessary API calls by filtering first
    """
    try:
        logger.info(f"🚀 Starting optimized token selection for {limit} tokens")

        # Use synchronous approach to avoid event loop issues
        try:
            # Step 1: Get KuCoin tokens with rate limiting
            kucoin_tokens = _get_kucoin_tokens_with_rate_limit()
            if not kucoin_tokens:
                logger.warning("No KuCoin tokens available, using fallback")
                return _get_fallback_tokens(limit)

            logger.info(f"📊 Retrieved {len(kucoin_tokens)} tokens from KuCoin")

            # Step 2: Pre-filter tokens based on volume, market cap, and basic criteria
            filtered_tokens = _pre_filter_tokens(
                kucoin_tokens, limit * 3
            )  # Get 3x for better selection
            logger.info(f"🔍 Pre-filtered to {len(filtered_tokens)} promising tokens")

            # Step 3: Priority Enhancement - TokenMetrics FIRST, then CoinGecko if needed
            enhanced_tokens = _enhance_with_priority_system(filtered_tokens)
            logger.info(
                f"📈 Enhanced {len(enhanced_tokens)} tokens with priority data system"
            )

            # Step 4: Final scoring and selection
            final_tokens = _calculate_final_scores(enhanced_tokens)[:limit]
            logger.info(f"✅ Selected top {len(final_tokens)} tokens for discovery")

            return final_tokens

        except RuntimeError as re:
            if "event loop" in str(re).lower():
                logger.warning(f"Event loop issue detected, using fallback: {re}")
                return _get_fallback_tokens(limit)
            else:
                raise

    except Exception as e:
        logger.error(f"Error in optimized token selection: {e}")
        return _get_fallback_tokens(limit)


def update_enhanced_discover_tokens(limit: int = 30) -> Dict:
    """
    Update enhanced discover tokens and return status
    """
    try:
        tokens = get_enhanced_discover_tokens(limit)
        return {
            "status": "success",
            "tokens_count": len(tokens),
            "message": f"Enhanced discover tokens updated with {len(tokens)} tokens",
        }
    except Exception as e:
        logger.error(f"Failed to update enhanced discover tokens: {e}")
        return {"status": "error", "message": str(e)}


if __name__ == "__main__":
    # Test the enhanced token selector
    logger.info("Testing Enhanced Token Selector...")
    result = update_enhanced_discover_tokens(25)
    print(f"Result: {result}")

#!/usr/bin/env python3
"""
Bot Management Testing Script
============================
Test the frontend-backend integration for bot management
"""

import sys
import asyncio
import requests
import time
from datetime import datetime

# Add backend to path
sys.path.append('./backend')

def test_api_endpoints():
    """Test the bot management API endpoints"""
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        ("GET", "/api/alpha-bot/status", "AlphaBot Status"),
        ("GET", "/api/micro-bot/status", "MicroBot Status"),
        ("GET", "/health", "Health Check"),
    ]
    
    print("🧪 TESTING API ENDPOINTS")
    print("=" * 50)
    
    for method, endpoint, name in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{base_url}{endpoint}", timeout=5)
            
            status = "✅ PASS" if response.status_code == 200 else f"❌ FAIL ({response.status_code})"
            print(f"{name}: {status}")
            
            if response.status_code == 200:
                data = response.json()
                if 'status' in data:
                    print(f"  Status: {data['status']}")
                if 'message' in data:
                    print(f"  Message: {data['message']}")
            
        except Exception as e:
            print(f"{name}: ❌ ERROR - {e}")
        
        print()

def test_microbot_startup():
    """Test MicroBot startup specifically"""
    print("🤖 TESTING ENHANCED MICROBOT STARTUP")
    print("=" * 50)
    
    try:
        # Import and test MicroBot directly
        from micro_bot import micro_bot_instance, start_micro_bot, get_micro_bot_status
        
        print("✅ MicroBot imports successful")
        
        # Test configuration
        print(f"Max portfolio size: {micro_bot_instance.max_portfolio_size}")
        print(f"Min confidence: {micro_bot_instance.min_confidence_score}")
        print(f"Profit levels: {len(micro_bot_instance.micro_profit_levels)}")
        
        # Test status function
        status = get_micro_bot_status()
        print(f"Current status: {status}")
        
        print("✅ MicroBot configuration test passed")
        
    except Exception as e:
        print(f"❌ MicroBot test failed: {e}")
        import traceback
        traceback.print_exc()

def test_alphabot_startup():
    """Test AlphaBot startup"""
    print("🚀 TESTING ALPHABOT STARTUP")
    print("=" * 50)
    
    try:
        # Test Advanced Trading Orchestrator
        from advanced_trading_orchestrator import advanced_orchestrator
        
        print("✅ Advanced Trading Orchestrator imports successful")
        
        # Test configuration
        print(f"Max positions: {advanced_orchestrator.max_positions}")
        print(f"Min confidence: {advanced_orchestrator.min_confidence}")
        print(f"Profit levels: {len(advanced_orchestrator.default_profit_levels)}")
        
        # Test portfolio summary
        summary = advanced_orchestrator.get_portfolio_summary()
        print(f"Portfolio summary: {summary['total_positions']} positions")
        
        print("✅ AlphaBot configuration test passed")
        
    except Exception as e:
        print(f"❌ AlphaBot test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_bot_functions():
    """Test bot functions directly"""
    print("⚙️ TESTING BOT FUNCTIONS")
    print("=" * 50)
    
    try:
        # Test MicroBot functions
        from micro_bot import analyze_token
        
        print("Testing MicroBot token analysis...")
        result = await analyze_token("BTC-USDT")
        if result:
            print(f"✅ Token analysis successful: {result.get('decision', 'N/A')}")
        else:
            print("❌ Token analysis failed")
        
    except Exception as e:
        print(f"❌ Bot function test failed: {e}")

def test_configuration_files():
    """Test configuration files and dependencies"""
    print("📁 TESTING CONFIGURATION")
    print("=" * 50)
    
    import os
    from dotenv import load_dotenv
    
    # Load environment
    load_dotenv('./backend/.env')
    
    # Check critical configs
    configs = [
        ("TRADING_MODE", os.getenv("TRADING_MODE")),
        ("KUCOIN_API_KEY", "SET" if os.getenv("KUCOIN_API_KEY") else "NOT SET"),
        ("OPENAI_API_KEY", "SET" if os.getenv("OPENAI_API_KEY") else "NOT SET"),
        ("GEMINI_API_KEY", "SET" if os.getenv("GEMINI_API_KEY") else "NOT SET"),
    ]
    
    for name, value in configs:
        print(f"{name}: {value}")
    
    print()
    
    # Check file existence
    files = [
        "./backend/micro_bot.py",
        "./backend/advanced_trading_orchestrator.py",
        "./backend/real_trade_executor.py",
        "./backend/main.py",
    ]
    
    for file_path in files:
        exists = "✅ EXISTS" if os.path.exists(file_path) else "❌ MISSING"
        print(f"{file_path}: {exists}")

async def main():
    """Main test function"""
    print("🧪 BOT MANAGEMENT INTEGRATION TEST")
    print("=" * 60)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    test_configuration_files()
    print()
    
    test_microbot_startup()
    print()
    
    test_alphabot_startup()
    print()
    
    await test_bot_functions()
    print()
    
    print("🌐 TESTING API ENDPOINTS (requires backend running)")
    print("Note: Start backend with 'cd backend && python main.py' first")
    print()
    
    try:
        test_api_endpoints()
    except Exception as e:
        print(f"❌ API tests skipped - backend not running: {e}")
    
    print("📋 TEST SUMMARY")
    print("=" * 40)
    print("✅ Configuration files checked")
    print("✅ MicroBot startup tested")
    print("✅ AlphaBot startup tested")
    print("✅ Bot functions tested")
    print()
    print("🚀 NEXT STEPS:")
    print("1. Start backend: cd backend && python main.py")
    print("2. Test frontend buttons in browser")
    print("3. Check bot logs for any errors")
    print("4. Monitor bot status in dashboard")

if __name__ == "__main__":
    asyncio.run(main())

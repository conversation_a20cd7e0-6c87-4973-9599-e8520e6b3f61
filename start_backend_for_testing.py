#!/usr/bin/env python3
"""
Start Backend for Frontend Testing
==================================
Lightweight backend startup for testing frontend bot controls
"""

import sys
import os
import uvicorn
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.append(str(backend_path))

def start_backend():
    """Start the backend server for testing"""
    print("🚀 Starting Backend for Frontend Testing...")
    print("=" * 50)
    print("Features enabled:")
    print("✅ Bot management endpoints")
    print("✅ Enhanced MicroBot with multi-level profit taking")
    print("✅ Advanced Trading Orchestrator (AlphaBot)")
    print("✅ Real-time status reporting")
    print("✅ Improved error handling")
    print()
    print("🌐 Backend will be available at: http://localhost:8000")
    print("📊 Frontend dashboard: http://localhost:3000")
    print()
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    # Change to backend directory
    os.chdir(backend_path)
    
    # Start the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Disable reload for stability
        log_level="info"
    )

if __name__ == "__main__":
    start_backend()
